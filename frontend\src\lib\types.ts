// User types
export interface User {
  id: string;
  username: string;
  email: string;
  display_name: string;
  bio?: string;
  avatar_url?: string;
  is_admin: boolean;
  is_online: boolean;
  last_seen?: string;
  created_at: string;
  updated_at: string;
}

// Message types
export interface Message {
  id: string;
  content: string;
  sender_id: string;
  sender: User;
  room_id?: string;
  recipient_id?: string;
  attachments: Attachment[];
  reactions: Reaction[];
  is_edited: boolean;
  created_at: string;
  updated_at: string;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
}

// Attachment types
export interface Attachment {
  id: string;
  filename: string;
  original_filename: string;
  file_type: string;
  file_size: number;
  url: string;
  thumbnail_url?: string;
  created_at: string;
}

// Reaction types
export interface Reaction {
  id: string;
  emoji: string;
  users: string[];
  message_id: string;
  created_at: string;
}

// Room types
export interface Room {
  id: string;
  name: string;
  description?: string;
  is_private: boolean;
  created_by: string;
  participants: User[];
  last_message?: Message;
  unread_count: number;
  created_at: string;
  updated_at: string;
}

// Chat types
export interface ChatRoom {
  id: string;
  name: string;
  type: 'public' | 'private' | 'direct';
  participants: User[];
  messages: Message[];
  isTyping: { [userId: string]: boolean };
  lastMessage?: Message;
  unreadCount: number;
}

// Typing indicator
export interface TypingIndicator {
  userId: string;
  username: string;
  roomId: string;
}

// Notification types
export interface Notification {
  id: string;
  type: 'message' | 'mention' | 'room_invite' | 'system';
  title: string;
  message: string;
  data?: any;
  is_read: boolean;
  created_at: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Form types
export interface LoginForm {
  username: string;
  password: string;
}

export interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  display_name?: string;
}

export interface ProfileForm {
  username: string;
  email: string;
  display_name: string;
  bio: string;
}

export interface PasswordChangeForm {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

export interface CreateRoomForm {
  name: string;
  description: string;
  is_private: boolean;
  participants: string[];
}

// Admin types
export interface AdminStats {
  total_users: number;
  online_users: number;
  total_messages: number;
  messages_today: number;
  total_rooms: number;
  active_rooms: number;
}

export interface AdminUser extends User {
  message_count: number;
  room_count: number;
}

// Socket event types
export interface SocketEvents {
  // Message events
  send_message: (data: {
    content: string;
    room_id?: string;
    recipient_id?: string;
    attachments?: any[];
  }) => void;
  
  new_message: (message: Message) => void;
  message_edited: (data: { messageId: string; content: string }) => void;
  message_deleted: (data: { messageId: string }) => void;
  
  // Room events
  join_room: (roomId: string) => void;
  leave_room: (roomId: string) => void;
  user_joined: (user: User) => void;
  user_left: (user: User) => void;
  
  // Typing events
  typing_start: (data: { roomId: string }) => void;
  typing_stop: (data: { roomId: string }) => void;
  
  // Reaction events
  add_reaction: (data: { messageId: string; emoji: string }) => void;
  remove_reaction: (data: { messageId: string; emoji: string }) => void;
  reaction_added: (data: { messageId: string; emoji: string; userId: string }) => void;
  reaction_removed: (data: { messageId: string; emoji: string; userId: string }) => void;
  
  // Status events
  user_status_change: (data: { userId: string; status: 'online' | 'offline' }) => void;
}

// Theme types
export type Theme = 
  | 'light' | 'dark' | 'cupcake' | 'bumblebee' | 'emerald' | 'corporate'
  | 'synthwave' | 'retro' | 'cyberpunk' | 'valentine' | 'halloween' | 'garden'
  | 'forest' | 'aqua' | 'lofi' | 'pastel' | 'fantasy' | 'wireframe' | 'black'
  | 'luxury' | 'dracula' | 'cmyk' | 'autumn' | 'business' | 'acid' | 'lemonade'
  | 'night' | 'coffee' | 'winter';

// Context types
export interface AuthContextType {
  user: User | null;
  login: (credentials: LoginForm) => Promise<void>;
  register: (userData: RegisterForm) => Promise<void>;
  logout: () => void;
  loading: boolean;
  isAuthenticated: boolean;
  updateUser: (user: User) => void;
}

export interface ChatContextType {
  rooms: Room[];
  currentRoom: Room | null;
  messages: Message[];
  onlineUsers: User[];
  typingUsers: TypingIndicator[];
  setCurrentRoom: (room: Room | null) => void;
  sendMessage: (content: string, attachments?: File[]) => void;
  editMessage: (messageId: string, content: string) => void;
  deleteMessage: (messageId: string) => void;
  addReaction: (messageId: string, emoji: string) => void;
  removeReaction: (messageId: string, emoji: string) => void;
  joinRoom: (roomId: string) => void;
  leaveRoom: (roomId: string) => void;
  createRoom: (roomData: CreateRoomForm) => Promise<Room>;
  loading: boolean;
}
