'use client';

import { useState, useEffect } from 'react';
import { Search, Edit, Trash2, UserPlus, ArrowLeft, MoreVertical, Shield, User } from 'lucide-react';
import Link from 'next/link';
import { useAuth, withAuth, useRequireAdmin } from '@/contexts/AuthContext';
import { adminAPI } from '@/lib/api';
import { AdminUser } from '@/lib/types';
import { formatDate } from '@/lib/utils';
import toast from 'react-hot-toast';

function UserManagement() {
  const { user: currentUser } = useAuth();
  const isAdmin = useRequireAdmin();
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [deletingUser, setDeletingUser] = useState<string | null>(null);

  useEffect(() => {
    if (isAdmin) {
      loadUsers();
    }
  }, [isAdmin, page]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await adminAPI.getUsers(page, 20);
      if (response.success) {
        setUsers(response.data.items);
        setTotalPages(response.data.total_pages);
      }
    } catch (error) {
      console.error('Failed to load users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async (userId: string, username: string) => {
    if (userId === currentUser?.id) {
      toast.error('You cannot delete your own account');
      return;
    }

    if (!confirm(`Are you sure you want to delete user "${username}"? This action cannot be undone.`)) {
      return;
    }

    try {
      setDeletingUser(userId);
      const response = await adminAPI.deleteUser(userId);
      if (response.success) {
        setUsers(prev => prev.filter(u => u.id !== userId));
        toast.success('User deleted successfully');
      }
    } catch (error: any) {
      console.error('Failed to delete user:', error);
      const message = error.response?.data?.message || 'Failed to delete user';
      toast.error(message);
    } finally {
      setDeletingUser(null);
    }
  };

  const filteredUsers = users.filter(user =>
    user.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-base-100">
      {/* Header */}
      <div className="bg-base-200 border-b border-base-300">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/admin" className="btn btn-ghost btn-sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Link>
              <div>
                <h1 className="text-2xl font-bold">User Management</h1>
                <p className="text-base-content/70">Manage user accounts and permissions</p>
              </div>
            </div>
            <Link href="/admin/users/create" className="btn btn-primary">
              <UserPlus className="h-4 w-4 mr-2" />
              Create User
            </Link>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Search and Filters */}
        <div className="card bg-base-200 mb-6">
          <div className="card-body">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-base-content/50" />
                  <input
                    type="text"
                    placeholder="Search users by name, username, or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="input input-bordered w-full pl-10"
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-base-content/70">
                  {filteredUsers.length} of {users.length} users
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Users Table */}
        <div className="card bg-base-200">
          <div className="card-body p-0">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="loading loading-spinner loading-lg"></div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="table table-zebra">
                  <thead>
                    <tr>
                      <th>User</th>
                      <th>Email</th>
                      <th>Role</th>
                      <th>Status</th>
                      <th>Activity</th>
                      <th>Joined</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredUsers.map((user) => (
                      <tr key={user.id}>
                        {/* User Info */}
                        <td>
                          <div className="flex items-center space-x-3">
                            <div className="avatar">
                              <div className="w-10 h-10 rounded-full bg-primary text-primary-content flex items-center justify-center">
                                {user.avatar_url ? (
                                  <img 
                                    src={user.avatar_url} 
                                    alt={user.display_name}
                                    className="w-full h-full rounded-full"
                                  />
                                ) : (
                                  <span className="text-sm font-medium">
                                    {user.display_name.charAt(0).toUpperCase()}
                                  </span>
                                )}
                              </div>
                            </div>
                            <div>
                              <div className="font-medium">{user.display_name}</div>
                              <div className="text-sm text-base-content/70">@{user.username}</div>
                            </div>
                          </div>
                        </td>

                        {/* Email */}
                        <td>
                          <span className="text-sm">{user.email}</span>
                        </td>

                        {/* Role */}
                        <td>
                          <div className="flex items-center space-x-2">
                            {user.is_admin ? (
                              <>
                                <Shield className="h-4 w-4 text-primary" />
                                <span className="badge badge-primary badge-sm">Admin</span>
                              </>
                            ) : (
                              <>
                                <User className="h-4 w-4 text-base-content/50" />
                                <span className="badge badge-ghost badge-sm">User</span>
                              </>
                            )}
                          </div>
                        </td>

                        {/* Status */}
                        <td>
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${
                              user.is_online ? 'bg-success' : 'bg-base-300'
                            }`} />
                            <span className="text-sm">
                              {user.is_online ? 'Online' : 'Offline'}
                            </span>
                          </div>
                        </td>

                        {/* Activity */}
                        <td>
                          <div className="text-sm">
                            <div>{user.message_count} messages</div>
                            <div className="text-base-content/70">{user.room_count} rooms</div>
                          </div>
                        </td>

                        {/* Joined Date */}
                        <td>
                          <span className="text-sm">{formatDate(user.created_at)}</span>
                        </td>

                        {/* Actions */}
                        <td>
                          <div className="flex items-center space-x-2">
                            <Link
                              href={`/admin/users/${user.id}/edit`}
                              className="btn btn-ghost btn-xs"
                              title="Edit user"
                            >
                              <Edit className="h-3 w-3" />
                            </Link>
                            
                            {user.id !== currentUser?.id && (
                              <button
                                onClick={() => handleDeleteUser(user.id, user.username)}
                                disabled={deletingUser === user.id}
                                className="btn btn-ghost btn-xs text-error hover:bg-error hover:text-error-content"
                                title="Delete user"
                              >
                                {deletingUser === user.id ? (
                                  <div className="loading loading-spinner loading-xs"></div>
                                ) : (
                                  <Trash2 className="h-3 w-3" />
                                )}
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {filteredUsers.length === 0 && !loading && (
                  <div className="text-center py-12">
                    <div className="text-base-content/50">
                      {searchTerm ? 'No users found matching your search' : 'No users found'}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-6">
            <div className="join">
              <button
                onClick={() => setPage(p => Math.max(1, p - 1))}
                disabled={page === 1}
                className="join-item btn"
              >
                Previous
              </button>
              
              {[...Array(totalPages)].map((_, i) => (
                <button
                  key={i + 1}
                  onClick={() => setPage(i + 1)}
                  className={`join-item btn ${page === i + 1 ? 'btn-active' : ''}`}
                >
                  {i + 1}
                </button>
              ))}
              
              <button
                onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                disabled={page === totalPages}
                className="join-item btn"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default withAuth(UserManagement);
