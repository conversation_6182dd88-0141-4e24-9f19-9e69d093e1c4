'use client';

import { useState } from 'react';
import { Download, Eye, File, Image, Video, Music, ExternalLink } from 'lucide-react';
import { Attachment } from '@/lib/types';
import { isImageFile, isVideoFile, isAudioFile, formatFileSize } from '@/lib/utils';
import { fileAPI } from '@/lib/api';
import toast from 'react-hot-toast';

interface MessageAttachmentProps {
  attachment: Attachment;
  allAttachments?: Attachment[];
  onImageClick?: (attachment: Attachment, allImages: Attachment[]) => void;
  className?: string;
}

export default function MessageAttachment({
  attachment,
  allAttachments = [],
  onImageClick,
  className = ''
}: MessageAttachmentProps) {
  const [downloading, setDownloading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  const handleDownload = async () => {
    try {
      setDownloading(true);
      const blob = await fileAPI.downloadFile(attachment.id);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = attachment.original_filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success('File downloaded successfully');
    } catch (error) {
      console.error('Download failed:', error);
      toast.error('Failed to download file');
    } finally {
      setDownloading(false);
    }
  };

  const getFileIcon = () => {
    if (isImageFile(attachment.filename)) return <Image className="h-4 w-4" />;
    if (isVideoFile(attachment.filename)) return <Video className="h-4 w-4" />;
    if (isAudioFile(attachment.filename)) return <Music className="h-4 w-4" />;
    return <File className="h-4 w-4" />;
  };

  const handleImageClick = () => {
    if (onImageClick && allAttachments.length > 0) {
      const imageAttachments = allAttachments.filter(att => isImageFile(att.filename));
      onImageClick(attachment, imageAttachments);
    } else {
      setShowPreview(true);
    }
  };

  const renderImageAttachment = () => (
    <div className={`relative group ${className}`}>
      <img
        src={attachment.thumbnail_url || attachment.url}
        alt={attachment.original_filename}
        className="max-w-xs max-h-64 rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
        onClick={handleImageClick}
      />
      
      {/* Overlay with actions */}
      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
        <div className="flex space-x-2">
          <button
            onClick={handleImageClick}
            className="btn btn-sm btn-circle bg-black bg-opacity-50 text-white border-none hover:bg-opacity-70"
            title="View full size"
          >
            <Eye className="h-4 w-4" />
          </button>
          <button
            onClick={handleDownload}
            disabled={downloading}
            className="btn btn-sm btn-circle bg-black bg-opacity-50 text-white border-none hover:bg-opacity-70"
            title="Download"
          >
            {downloading ? (
              <div className="loading loading-spinner loading-xs"></div>
            ) : (
              <Download className="h-4 w-4" />
            )}
          </button>
        </div>
      </div>

      {/* File info */}
      <div className="mt-1">
        <p className="text-xs text-base-content/70 truncate">
          {attachment.original_filename}
        </p>
        <p className="text-xs text-base-content/50">
          {formatFileSize(attachment.file_size)}
        </p>
      </div>

      {/* Preview Modal */}
      {showPreview && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <img
              src={attachment.url}
              alt={attachment.original_filename}
              className="max-w-full max-h-full object-contain"
            />
            <button
              onClick={() => setShowPreview(false)}
              className="absolute top-4 right-4 btn btn-circle btn-sm bg-black bg-opacity-50 text-white border-none"
            >
              ✕
            </button>
            <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white p-2 rounded">
              <p className="text-sm font-medium">{attachment.original_filename}</p>
              <p className="text-xs opacity-75">{formatFileSize(attachment.file_size)}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderVideoAttachment = () => (
    <div className={`max-w-xs ${className}`}>
      <video
        controls
        className="w-full max-h-64 rounded-lg"
        poster={attachment.thumbnail_url}
      >
        <source src={attachment.url} type={attachment.file_type} />
        Your browser does not support the video tag.
      </video>
      
      <div className="mt-1 flex items-center justify-between">
        <div>
          <p className="text-xs text-base-content/70 truncate">
            {attachment.original_filename}
          </p>
          <p className="text-xs text-base-content/50">
            {formatFileSize(attachment.file_size)}
          </p>
        </div>
        <button
          onClick={handleDownload}
          disabled={downloading}
          className="btn btn-ghost btn-xs"
          title="Download"
        >
          {downloading ? (
            <div className="loading loading-spinner loading-xs"></div>
          ) : (
            <Download className="h-3 w-3" />
          )}
        </button>
      </div>
    </div>
  );

  const renderAudioAttachment = () => (
    <div className={`max-w-xs ${className}`}>
      <div className="bg-base-200 p-3 rounded-lg">
        <div className="flex items-center space-x-3 mb-2">
          <Music className="h-5 w-5 text-primary" />
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{attachment.original_filename}</p>
            <p className="text-xs text-base-content/70">{formatFileSize(attachment.file_size)}</p>
          </div>
        </div>
        
        <audio controls className="w-full">
          <source src={attachment.url} type={attachment.file_type} />
          Your browser does not support the audio tag.
        </audio>
      </div>
      
      <div className="mt-1 flex justify-end">
        <button
          onClick={handleDownload}
          disabled={downloading}
          className="btn btn-ghost btn-xs"
          title="Download"
        >
          {downloading ? (
            <div className="loading loading-spinner loading-xs"></div>
          ) : (
            <Download className="h-3 w-3" />
          )}
        </button>
      </div>
    </div>
  );

  const renderFileAttachment = () => (
    <div className={`max-w-xs ${className}`}>
      <div className="bg-base-200 p-3 rounded-lg hover:bg-base-300 transition-colors">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            {getFileIcon()}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{attachment.original_filename}</p>
            <div className="flex items-center space-x-2 text-xs text-base-content/70">
              <span>{formatFileSize(attachment.file_size)}</span>
              <span>•</span>
              <span>{attachment.file_type}</span>
            </div>
          </div>
          <div className="flex space-x-1">
            <button
              onClick={() => window.open(attachment.url, '_blank')}
              className="btn btn-ghost btn-xs"
              title="Open in new tab"
            >
              <ExternalLink className="h-3 w-3" />
            </button>
            <button
              onClick={handleDownload}
              disabled={downloading}
              className="btn btn-ghost btn-xs"
              title="Download"
            >
              {downloading ? (
                <div className="loading loading-spinner loading-xs"></div>
              ) : (
                <Download className="h-3 w-3" />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // Render based on file type
  if (isImageFile(attachment.filename)) {
    return renderImageAttachment();
  } else if (isVideoFile(attachment.filename)) {
    return renderVideoAttachment();
  } else if (isAudioFile(attachment.filename)) {
    return renderAudioAttachment();
  } else {
    return renderFileAttachment();
  }
}
