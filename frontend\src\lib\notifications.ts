class NotificationManager {
  private permission: NotificationPermission = 'default';

  constructor() {
    this.checkPermission();
  }

  private checkPermission() {
    if ('Notification' in window) {
      this.permission = Notification.permission;
    }
  }

  async requestPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    if (this.permission === 'granted') {
      return true;
    }

    if (this.permission === 'denied') {
      return false;
    }

    const permission = await Notification.requestPermission();
    this.permission = permission;
    return permission === 'granted';
  }

  canShowNotifications(): boolean {
    return 'Notification' in window && this.permission === 'granted';
  }

  showNotification(title: string, options?: NotificationOptions): Notification | null {
    if (!this.canShowNotifications()) {
      return null;
    }

    const defaultOptions: NotificationOptions = {
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      tag: 'chat-notification',
      renotify: true,
      ...options,
    };

    try {
      const notification = new Notification(title, defaultOptions);

      // Auto-close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);

      return notification;
    } catch (error) {
      console.error('Failed to show notification:', error);
      return null;
    }
  }

  showMessageNotification(
    senderName: string,
    message: string,
    roomName?: string,
    onClick?: () => void
  ): Notification | null {
    const title = roomName ? `${senderName} in #${roomName}` : senderName;
    const body = message.length > 100 ? message.substring(0, 100) + '...' : message;

    const notification = this.showNotification(title, {
      body,
      icon: '/favicon.ico',
      tag: `message-${Date.now()}`,
    });

    if (notification && onClick) {
      notification.onclick = () => {
        window.focus();
        onClick();
        notification.close();
      };
    }

    return notification;
  }

  showSystemNotification(message: string): Notification | null {
    return this.showNotification('ChatApp', {
      body: message,
      icon: '/favicon.ico',
      tag: 'system-notification',
    });
  }
}

// Singleton instance
const notificationManager = new NotificationManager();

export default notificationManager;

// Utility functions
export const requestNotificationPermission = () => {
  return notificationManager.requestPermission();
};

export const showMessageNotification = (
  senderName: string,
  message: string,
  roomName?: string,
  onClick?: () => void
) => {
  return notificationManager.showMessageNotification(senderName, message, roomName, onClick);
};

export const showSystemNotification = (message: string) => {
  return notificationManager.showSystemNotification(message);
};

export const canShowNotifications = () => {
  return notificationManager.canShowNotifications();
};