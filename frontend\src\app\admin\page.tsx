'use client';

import { useState, useEffect } from 'react';
import { Users, MessageSquare, Hash, TrendingUp, ArrowLeft, Settings, UserPlus, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { useAuth, withAuth, useRequireAdmin } from '@/contexts/AuthContext';
import { adminAPI } from '@/lib/api';
import { AdminStats } from '@/lib/types';
import { formatDate } from '@/lib/utils';
import toast from 'react-hot-toast';

function AdminDashboard() {
  const { user } = useAuth();
  const isAdmin = useRequireAdmin();
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isAdmin) {
      loadStats();
    }
  }, [isAdmin]);

  const loadStats = async () => {
    try {
      setLoading(true);
      const response = await adminAPI.getStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('Failed to load stats:', error);
      toast.error('Failed to load dashboard statistics');
    } finally {
      setLoading(false);
    }
  };

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-base-100">
      {/* Header */}
      <div className="bg-base-200 border-b border-base-300">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/chat" className="btn btn-ghost btn-sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Chat
              </Link>
              <div>
                <h1 className="text-2xl font-bold">Admin Dashboard</h1>
                <p className="text-base-content/70">Manage users, rooms, and system settings</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-base-content/70">Welcome back,</span>
              <span className="font-medium">{user?.display_name}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Stats Cards */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="card bg-base-200 animate-pulse">
                <div className="card-body">
                  <div className="h-4 bg-base-300 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-base-300 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : stats ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Total Users */}
            <div className="card bg-gradient-to-br from-primary to-primary-focus text-primary-content">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-primary-content/80 text-sm">Total Users</p>
                    <p className="text-3xl font-bold">{stats.total_users}</p>
                  </div>
                  <Users className="h-8 w-8 text-primary-content/60" />
                </div>
              </div>
            </div>

            {/* Online Users */}
            <div className="card bg-gradient-to-br from-success to-success-focus text-success-content">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-success-content/80 text-sm">Online Users</p>
                    <p className="text-3xl font-bold">{stats.online_users}</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-success-content/60" />
                </div>
              </div>
            </div>

            {/* Total Messages */}
            <div className="card bg-gradient-to-br from-info to-info-focus text-info-content">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-info-content/80 text-sm">Total Messages</p>
                    <p className="text-3xl font-bold">{stats.total_messages}</p>
                  </div>
                  <MessageSquare className="h-8 w-8 text-info-content/60" />
                </div>
              </div>
            </div>

            {/* Active Rooms */}
            <div className="card bg-gradient-to-br from-warning to-warning-focus text-warning-content">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-warning-content/80 text-sm">Active Rooms</p>
                    <p className="text-3xl font-bold">{stats.active_rooms}</p>
                  </div>
                  <Hash className="h-8 w-8 text-warning-content/60" />
                </div>
              </div>
            </div>
          </div>
        ) : null}

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* User Management */}
          <div className="card bg-base-200">
            <div className="card-body">
              <h2 className="card-title">User Management</h2>
              <p className="text-base-content/70 mb-4">
                Manage user accounts, permissions, and activity
              </p>
              <div className="space-y-2">
                <Link href="/admin/users" className="btn btn-primary w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  Manage Users
                </Link>
                <Link href="/admin/users/create" className="btn btn-outline w-full justify-start">
                  <UserPlus className="h-4 w-4 mr-2" />
                  Create New User
                </Link>
              </div>
            </div>
          </div>

          {/* Room Management */}
          <div className="card bg-base-200">
            <div className="card-body">
              <h2 className="card-title">Room Management</h2>
              <p className="text-base-content/70 mb-4">
                Manage chat rooms, permissions, and moderation
              </p>
              <div className="space-y-2">
                <Link href="/admin/rooms" className="btn btn-primary w-full justify-start">
                  <Hash className="h-4 w-4 mr-2" />
                  Manage Rooms
                </Link>
                <button className="btn btn-outline w-full justify-start">
                  <Settings className="h-4 w-4 mr-2" />
                  Room Settings
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="card bg-base-200">
          <div className="card-body">
            <h2 className="card-title mb-4">System Overview</h2>
            
            {stats && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Messages Today */}
                <div className="stat">
                  <div className="stat-title">Messages Today</div>
                  <div className="stat-value text-primary">{stats.messages_today}</div>
                  <div className="stat-desc">
                    {stats.total_messages > 0 
                      ? `${((stats.messages_today / stats.total_messages) * 100).toFixed(1)}% of total messages`
                      : 'No messages yet'
                    }
                  </div>
                </div>

                {/* Total Rooms */}
                <div className="stat">
                  <div className="stat-title">Total Rooms</div>
                  <div className="stat-value text-secondary">{stats.total_rooms}</div>
                  <div className="stat-desc">
                    {stats.active_rooms} currently active
                  </div>
                </div>
              </div>
            )}

            {/* System Health */}
            <div className="mt-6">
              <h3 className="font-semibold mb-3">System Health</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-success rounded-full"></div>
                  <span className="text-sm">Database: Online</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-success rounded-full"></div>
                  <span className="text-sm">WebSocket: Connected</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-success rounded-full"></div>
                  <span className="text-sm">File Storage: Available</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default withAuth(AdminDashboard);
