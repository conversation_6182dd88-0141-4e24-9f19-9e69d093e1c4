import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from '@/contexts/AuthContext';
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "ChatApp - Real-Time Messaging",
  description: "A modern real-time chat application built with Next.js and FastAPI",
  keywords: ["chat", "messaging", "real-time", "websocket", "nextjs", "fastapi"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" data-theme="light">
      <body className={`${inter.variable} font-sans antialiased`}>
        <AuthProvider>
          {children}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'var(--fallback-b1,oklch(var(--b1)))',
                color: 'var(--fallback-bc,oklch(var(--bc)))',
                border: '1px solid var(--fallback-b3,oklch(var(--b3)))',
              },
              success: {
                iconTheme: {
                  primary: 'var(--fallback-su,oklch(var(--su)))',
                  secondary: 'var(--fallback-suc,oklch(var(--suc)))',
                },
              },
              error: {
                iconTheme: {
                  primary: 'var(--fallback-er,oklch(var(--er)))',
                  secondary: 'var(--fallback-erc,oklch(var(--erc)))',
                },
              },
            }}
          />
        </AuthProvider>
      </body>
    </html>
  );
}
