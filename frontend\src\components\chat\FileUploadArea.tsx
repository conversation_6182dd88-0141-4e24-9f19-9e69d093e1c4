'use client';

import { useState, useRef, useCallback } from 'react';
import { Upload, X, File, Image, Video, Music } from 'lucide-react';
import { isImageFile, isVideoFile, isAudioFile, formatFileSize } from '@/lib/utils';
import toast from 'react-hot-toast';

interface FileUploadAreaProps {
  onFilesSelected: (files: File[]) => void;
  maxFiles?: number;
  maxFileSize?: number; // in MB
  acceptedTypes?: string[];
  className?: string;
}

export default function FileUploadArea({
  onFilesSelected,
  maxFiles = 10,
  maxFileSize = 50, // 50MB default
  acceptedTypes = ['image/*', 'video/*', 'audio/*', '.pdf', '.doc', '.docx', '.txt', '.zip', '.rar'],
  className = ''
}: FileUploadAreaProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): boolean => {
    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      toast.error(`File "${file.name}" is too large. Maximum size is ${maxFileSize}MB.`);
      return false;
    }

    // Check file type
    const isValidType = acceptedTypes.some(type => {
      if (type.includes('*')) {
        const category = type.split('/')[0];
        return file.type.startsWith(category);
      }
      return file.type === type || file.name.toLowerCase().endsWith(type);
    });

    if (!isValidType) {
      toast.error(`File type "${file.type}" is not supported.`);
      return false;
    }

    return true;
  };

  const handleFiles = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const validFiles = fileArray.filter(validateFile);

    if (selectedFiles.length + validFiles.length > maxFiles) {
      toast.error(`Maximum ${maxFiles} files allowed.`);
      return;
    }

    const newFiles = [...selectedFiles, ...validFiles];
    setSelectedFiles(newFiles);
    onFilesSelected(newFiles);
  }, [selectedFiles, maxFiles, onFilesSelected]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFiles(files);
    }
  }, [handleFiles]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFiles(files);
    }
  };

  const removeFile = (index: number) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);
    onFilesSelected(newFiles);
  };

  const clearFiles = () => {
    setSelectedFiles([]);
    onFilesSelected([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const getFileIcon = (file: File) => {
    if (isImageFile(file.name)) return <Image className="h-4 w-4" />;
    if (isVideoFile(file.name)) return <Video className="h-4 w-4" />;
    if (isAudioFile(file.name)) return <Music className="h-4 w-4" />;
    return <File className="h-4 w-4" />;
  };

  const getFilePreview = (file: File) => {
    if (isImageFile(file.name)) {
      const url = URL.createObjectURL(file);
      return (
        <img
          src={url}
          alt={file.name}
          className="w-16 h-16 object-cover rounded"
          onLoad={() => URL.revokeObjectURL(url)}
        />
      );
    }
    return (
      <div className="w-16 h-16 bg-base-200 rounded flex items-center justify-center">
        {getFileIcon(file)}
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Drop Zone */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
          ${isDragOver 
            ? 'border-primary bg-primary/10' 
            : 'border-base-300 hover:border-primary hover:bg-base-200'
          }
        `}
      >
        <Upload className="h-8 w-8 mx-auto mb-2 text-base-content/50" />
        <p className="text-sm text-base-content/70 mb-1">
          Drop files here or click to browse
        </p>
        <p className="text-xs text-base-content/50">
          Max {maxFiles} files, {maxFileSize}MB each
        </p>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptedTypes.join(',')}
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Selected Files */}
      {selectedFiles.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">
              Selected Files ({selectedFiles.length})
            </h4>
            <button
              onClick={clearFiles}
              className="btn btn-ghost btn-xs"
            >
              Clear All
            </button>
          </div>

          <div className="space-y-2 max-h-48 overflow-y-auto">
            {selectedFiles.map((file, index) => (
              <div
                key={`${file.name}-${index}`}
                className="flex items-center space-x-3 p-3 bg-base-200 rounded-lg"
              >
                {/* File preview */}
                <div className="flex-shrink-0">
                  {getFilePreview(file)}
                </div>

                {/* File info */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{file.name}</p>
                  <div className="flex items-center space-x-2 text-xs text-base-content/70">
                    <span>{formatFileSize(file.size)}</span>
                    <span>•</span>
                    <span>{file.type || 'Unknown type'}</span>
                  </div>
                </div>

                {/* Remove button */}
                <button
                  onClick={() => removeFile(index)}
                  className="btn btn-ghost btn-xs"
                  title="Remove file"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
