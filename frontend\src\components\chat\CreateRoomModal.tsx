'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { X } from 'lucide-react';
import { useChat } from '@/contexts/ChatContext';
import { CreateRoomForm } from '@/lib/types';

const createRoomSchema = z.object({
  name: z
    .string()
    .min(1, 'Room name is required')
    .max(50, 'Room name must be less than 50 characters')
    .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Room name can only contain letters, numbers, spaces, hyphens, and underscores'),
  description: z
    .string()
    .max(200, 'Description must be less than 200 characters')
    .optional(),
  is_private: z.boolean(),
  participants: z.array(z.string()).optional(),
});

interface CreateRoomModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function CreateRoomModal({ isOpen, onClose }: CreateRoomModalProps) {
  const { createRoom } = useChat();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CreateRoomForm>({
    resolver: zodResolver(createRoomSchema),
    defaultValues: {
      name: '',
      description: '',
      is_private: false,
      participants: [],
    },
  });

  const onSubmit = async (data: CreateRoomForm) => {
    try {
      setIsSubmitting(true);
      await createRoom(data);
      reset();
      onClose();
    } catch (error) {
      // Error is handled in the context
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-base-100 rounded-lg shadow-xl w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-base-300">
          <h2 className="text-xl font-semibold">Create New Room</h2>
          <button
            onClick={handleClose}
            className="btn btn-ghost btn-sm"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-4">
          {/* Room Name */}
          <div className="form-control">
            <label className="label">
              <span className="label-text font-medium">Room Name</span>
            </label>
            <input
              type="text"
              placeholder="Enter room name"
              className={`input input-bordered w-full ${
                errors.name ? 'input-error' : ''
              }`}
              {...register('name')}
            />
            {errors.name && (
              <label className="label">
                <span className="label-text-alt text-error">
                  {errors.name.message}
                </span>
              </label>
            )}
          </div>

          {/* Description */}
          <div className="form-control">
            <label className="label">
              <span className="label-text font-medium">Description (Optional)</span>
            </label>
            <textarea
              placeholder="Describe what this room is for..."
              className={`textarea textarea-bordered w-full ${
                errors.description ? 'textarea-error' : ''
              }`}
              rows={3}
              {...register('description')}
            />
            {errors.description && (
              <label className="label">
                <span className="label-text-alt text-error">
                  {errors.description.message}
                </span>
              </label>
            )}
          </div>

          {/* Privacy Setting */}
          <div className="form-control">
            <label className="label cursor-pointer">
              <span className="label-text font-medium">Private Room</span>
              <input
                type="checkbox"
                className="toggle toggle-primary"
                {...register('is_private')}
              />
            </label>
            <label className="label">
              <span className="label-text-alt text-base-content/70">
                Private rooms require an invitation to join
              </span>
            </label>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="btn btn-ghost flex-1"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`btn btn-primary flex-1 ${
                isSubmitting ? 'loading' : ''
              }`}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Creating...' : 'Create Room'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
