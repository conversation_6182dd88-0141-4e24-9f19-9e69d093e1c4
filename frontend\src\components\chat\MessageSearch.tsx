'use client';

import { useState, useEffect, useRef } from 'react';
import { Search, X, Calendar, User, Hash } from 'lucide-react';
import { useChat } from '@/contexts/ChatContext';
import { Message } from '@/lib/types';
import { formatDate, formatTime, highlightText } from '@/lib/utils';

interface MessageSearchProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function MessageSearch({ isOpen, onClose }: MessageSearchProps) {
  const { messages, currentRoom } = useChat();
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    if (searchTerm.trim()) {
      performSearch();
    } else {
      setSearchResults([]);
    }
  }, [searchTerm, messages]);

  const performSearch = async () => {
    if (!searchTerm.trim()) return;

    setLoading(true);
    
    // Simulate search delay for better UX
    await new Promise(resolve => setTimeout(resolve, 200));

    const results = messages.filter(message => {
      const searchLower = searchTerm.toLowerCase();
      return (
        message.content.toLowerCase().includes(searchLower) ||
        message.sender.display_name.toLowerCase().includes(searchLower) ||
        message.sender.username.toLowerCase().includes(searchLower)
      );
    });

    // Sort by relevance (exact matches first, then by date)
    const sortedResults = results.sort((a, b) => {
      const aExact = a.content.toLowerCase().includes(searchTerm.toLowerCase());
      const bExact = b.content.toLowerCase().includes(searchTerm.toLowerCase());
      
      if (aExact && !bExact) return -1;
      if (!aExact && bExact) return 1;
      
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    });

    setSearchResults(sortedResults);
    setLoading(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  const scrollToMessage = (messageId: string) => {
    // This would scroll to the message in the chat
    const messageElement = document.getElementById(`message-${messageId}`);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      messageElement.classList.add('highlight-message');
      setTimeout(() => {
        messageElement.classList.remove('highlight-message');
      }, 2000);
    }
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4 pt-20">
      <div className="bg-base-100 rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-base-300">
          <div className="flex items-center space-x-2">
            <Search className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-semibold">Search Messages</h2>
            {currentRoom && (
              <span className="text-sm text-base-content/70">
                in #{currentRoom.name}
              </span>
            )}
          </div>
          <button onClick={onClose} className="btn btn-ghost btn-sm">
            <X className="h-4 w-4" />
          </button>
        </div>

        {/* Search Input */}
        <div className="p-4 border-b border-base-300">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-base-content/50" />
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Search messages, users, or content..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleKeyDown}
              className="input input-bordered w-full pl-10"
            />
          </div>
          
          {searchTerm && (
            <div className="mt-2 text-sm text-base-content/70">
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="loading loading-spinner loading-xs"></div>
                  <span>Searching...</span>
                </div>
              ) : (
                <span>
                  {searchResults.length} result{searchResults.length !== 1 ? 's' : ''} found
                </span>
              )}
            </div>
          )}
        </div>

        {/* Search Results */}
        <div className="flex-1 overflow-y-auto">
          {!searchTerm ? (
            <div className="flex items-center justify-center py-12 text-base-content/50">
              <div className="text-center">
                <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Start typing to search messages</p>
                <p className="text-sm mt-1">Search by content, username, or display name</p>
              </div>
            </div>
          ) : loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="loading loading-spinner loading-lg"></div>
            </div>
          ) : searchResults.length === 0 ? (
            <div className="flex items-center justify-center py-12 text-base-content/50">
              <div className="text-center">
                <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No messages found</p>
                <p className="text-sm mt-1">Try different keywords</p>
              </div>
            </div>
          ) : (
            <div className="p-4 space-y-3">
              {searchResults.map((message) => (
                <button
                  key={message.id}
                  onClick={() => scrollToMessage(message.id)}
                  className="w-full text-left p-3 rounded-lg hover:bg-base-200 transition-colors border border-transparent hover:border-base-300"
                >
                  <div className="flex items-start space-x-3">
                    {/* Avatar */}
                    <div className="avatar">
                      <div className="w-8 h-8 rounded-full bg-primary text-primary-content flex items-center justify-center">
                        {message.sender.avatar_url ? (
                          <img 
                            src={message.sender.avatar_url} 
                            alt={message.sender.display_name}
                            className="w-full h-full rounded-full"
                          />
                        ) : (
                          <span className="text-xs font-medium">
                            {message.sender.display_name.charAt(0).toUpperCase()}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Message Content */}
                    <div className="flex-1 min-w-0">
                      {/* Header */}
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-sm">
                          {message.sender.display_name}
                        </span>
                        <span className="text-xs text-base-content/50">
                          {formatDate(message.created_at)} at {formatTime(message.created_at)}
                        </span>
                        {message.room_id && (
                          <div className="flex items-center space-x-1 text-xs text-base-content/50">
                            <Hash className="h-3 w-3" />
                            <span>{currentRoom?.name}</span>
                          </div>
                        )}
                      </div>

                      {/* Message Text */}
                      <div 
                        className="text-sm text-base-content/80"
                        dangerouslySetInnerHTML={{
                          __html: highlightText(message.content, searchTerm)
                        }}
                      />

                      {/* Attachments */}
                      {message.attachments.length > 0 && (
                        <div className="mt-2 text-xs text-base-content/50">
                          📎 {message.attachments.length} attachment{message.attachments.length !== 1 ? 's' : ''}
                        </div>
                      )}

                      {/* Reactions */}
                      {message.reactions.length > 0 && (
                        <div className="mt-2 flex space-x-1">
                          {message.reactions.slice(0, 3).map((reaction) => (
                            <span key={reaction.id} className="text-xs bg-base-300 px-1 rounded">
                              {reaction.emoji} {reaction.users.length}
                            </span>
                          ))}
                          {message.reactions.length > 3 && (
                            <span className="text-xs text-base-content/50">
                              +{message.reactions.length - 3} more
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-base-300 text-xs text-base-content/50">
          <div className="flex items-center justify-between">
            <span>Press Esc to close</span>
            <span>{searchResults.length} of {messages.length} messages</span>
          </div>
        </div>
      </div>
    </div>
  );
}
