'use client';

import { useState, useEffect } from 'react';
import { Search, Hash, Lock, Trash2, ArrowLeft, Users, MessageSquare, Calendar } from 'lucide-react';
import Link from 'next/link';
import { useAuth, withAuth, useRequireAdmin } from '@/contexts/AuthContext';
import { adminAPI } from '@/lib/api';
import { Room } from '@/lib/types';
import { formatDate } from '@/lib/utils';
import toast from 'react-hot-toast';

function RoomManagement() {
  const { user: currentUser } = useAuth();
  const isAdmin = useRequireAdmin();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [deletingRoom, setDeletingRoom] = useState<string | null>(null);

  useEffect(() => {
    if (isAdmin) {
      loadRooms();
    }
  }, [isAdmin]);

  const loadRooms = async () => {
    try {
      setLoading(true);
      const response = await adminAPI.getRooms();
      if (response.success) {
        setRooms(response.data);
      }
    } catch (error) {
      console.error('Failed to load rooms:', error);
      toast.error('Failed to load rooms');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRoom = async (roomId: string, roomName: string) => {
    if (!confirm(`Are you sure you want to delete room "${roomName}"? This action cannot be undone and will delete all messages in this room.`)) {
      return;
    }

    try {
      setDeletingRoom(roomId);
      const response = await adminAPI.deleteRoom(roomId);
      if (response.success) {
        setRooms(prev => prev.filter(r => r.id !== roomId));
        toast.success('Room deleted successfully');
      }
    } catch (error: any) {
      console.error('Failed to delete room:', error);
      const message = error.response?.data?.message || 'Failed to delete room';
      toast.error(message);
    } finally {
      setDeletingRoom(null);
    }
  };

  const filteredRooms = rooms.filter(room =>
    room.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (room.description && room.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-base-100">
      {/* Header */}
      <div className="bg-base-200 border-b border-base-300">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/admin" className="btn btn-ghost btn-sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Link>
              <div>
                <h1 className="text-2xl font-bold">Room Management</h1>
                <p className="text-base-content/70">Manage chat rooms and moderation</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Search */}
        <div className="card bg-base-200 mb-6">
          <div className="card-body">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-base-content/50" />
                  <input
                    type="text"
                    placeholder="Search rooms by name or description..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="input input-bordered w-full pl-10"
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-base-content/70">
                  {filteredRooms.length} of {rooms.length} rooms
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Rooms Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="card bg-base-200 animate-pulse">
                <div className="card-body">
                  <div className="h-4 bg-base-300 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-base-300 rounded w-full mb-4"></div>
                  <div className="h-3 bg-base-300 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredRooms.map((room) => (
              <div key={room.id} className="card bg-base-200 hover:shadow-lg transition-shadow">
                <div className="card-body">
                  {/* Room Header */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {room.is_private ? (
                        <Lock className="h-5 w-5 text-warning" />
                      ) : (
                        <Hash className="h-5 w-5 text-primary" />
                      )}
                      <h3 className="font-semibold truncate">{room.name}</h3>
                    </div>
                    <div className="dropdown dropdown-end">
                      <label tabIndex={0} className="btn btn-ghost btn-xs">
                        <Trash2 className="h-3 w-3" />
                      </label>
                      <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-32">
                        <li>
                          <button
                            onClick={() => handleDeleteRoom(room.id, room.name)}
                            disabled={deletingRoom === room.id}
                            className="text-error"
                          >
                            {deletingRoom === room.id ? (
                              <div className="loading loading-spinner loading-xs"></div>
                            ) : (
                              <>
                                <Trash2 className="h-3 w-3" />
                                Delete
                              </>
                            )}
                          </button>
                        </li>
                      </ul>
                    </div>
                  </div>

                  {/* Room Description */}
                  {room.description && (
                    <p className="text-sm text-base-content/70 mb-3 line-clamp-2">
                      {room.description}
                    </p>
                  )}

                  {/* Room Stats */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-1">
                        <Users className="h-3 w-3 text-base-content/50" />
                        <span>Participants</span>
                      </div>
                      <span className="font-medium">{room.participants.length}</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3 text-base-content/50" />
                        <span>Created</span>
                      </div>
                      <span className="font-medium">{formatDate(room.created_at)}</span>
                    </div>

                    {room.last_message && (
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-1">
                          <MessageSquare className="h-3 w-3 text-base-content/50" />
                          <span>Last message</span>
                        </div>
                        <span className="font-medium">{formatDate(room.last_message.created_at)}</span>
                      </div>
                    )}
                  </div>

                  {/* Room Type Badge */}
                  <div className="flex items-center justify-between">
                    <div className="flex space-x-2">
                      {room.is_private ? (
                        <span className="badge badge-warning badge-sm">Private</span>
                      ) : (
                        <span className="badge badge-primary badge-sm">Public</span>
                      )}
                    </div>
                    
                    {/* Participants Preview */}
                    <div className="flex -space-x-1">
                      {room.participants.slice(0, 3).map(participant => (
                        <div
                          key={participant.id}
                          className="avatar"
                          title={participant.display_name}
                        >
                          <div className="w-6 h-6 rounded-full bg-primary text-primary-content flex items-center justify-center border-2 border-base-100">
                            {participant.avatar_url ? (
                              <img 
                                src={participant.avatar_url} 
                                alt={participant.display_name}
                                className="w-full h-full rounded-full"
                              />
                            ) : (
                              <span className="text-xs font-medium">
                                {participant.display_name.charAt(0).toUpperCase()}
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                      {room.participants.length > 3 && (
                        <div className="w-6 h-6 rounded-full bg-base-300 text-base-content flex items-center justify-center border-2 border-base-100 text-xs">
                          +{room.participants.length - 3}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Last Message Preview */}
                  {room.last_message && (
                    <div className="mt-3 p-2 bg-base-300 rounded text-xs">
                      <div className="flex items-center space-x-1 mb-1">
                        <span className="font-medium">{room.last_message.sender.display_name}:</span>
                      </div>
                      <p className="truncate text-base-content/70">
                        {room.last_message.content}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {filteredRooms.length === 0 && !loading && (
          <div className="text-center py-12">
            <Hash className="h-12 w-12 text-base-content/30 mx-auto mb-4" />
            <div className="text-base-content/50">
              {searchTerm ? 'No rooms found matching your search' : 'No rooms found'}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default withAuth(RoomManagement);
