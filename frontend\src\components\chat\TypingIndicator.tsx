'use client';

import { TypingIndicator as TypingIndicatorType } from '@/lib/types';

interface TypingIndicatorProps {
  users: TypingIndicatorType[];
}

export default function TypingIndicator({ users }: TypingIndicatorProps) {
  if (users.length === 0) {
    return null;
  }

  const getTypingText = () => {
    if (users.length === 1) {
      return `${users[0].username} is typing...`;
    } else if (users.length === 2) {
      return `${users[0].username} and ${users[1].username} are typing...`;
    } else {
      return `${users[0].username} and ${users.length - 1} others are typing...`;
    }
  };

  return (
    <div className="flex items-center space-x-2 py-2 px-4">
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-base-content/50 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
        <div className="w-2 h-2 bg-base-content/50 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
        <div className="w-2 h-2 bg-base-content/50 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
      </div>
      <span className="text-sm text-base-content/70 italic">
        {getTypingText()}
      </span>
    </div>
  );
}
