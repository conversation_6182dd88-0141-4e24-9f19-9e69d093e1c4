import Link from 'next/link';
import { MessageCircle, Users, Shield, Zap, Globe, Code } from 'lucide-react';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 via-base-100 to-secondary/10">
      {/* Navigation */}
      <nav className="navbar bg-base-100/80 backdrop-blur-sm border-b border-base-300">
        <div className="navbar-start">
          <div className="flex items-center space-x-2">
            <MessageCircle className="h-8 w-8 text-primary" />
            <span className="text-xl font-bold">ChatApp</span>
          </div>
        </div>
        <div className="navbar-end">
          <div className="flex space-x-2">
            <Link href="/login" className="btn btn-ghost">
              Login
            </Link>
            <Link href="/register" className="btn btn-primary">
              Get Started
            </Link>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="hero min-h-[80vh] bg-base-100">
        <div className="hero-content text-center">
          <div className="max-w-4xl">
            <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Real-Time Chat Application
            </h1>
            <p className="text-xl mb-8 text-base-content/80">
              Connect, communicate, and collaborate with our modern chat platform.
              Built with cutting-edge technology for seamless real-time messaging.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/register" className="btn btn-primary btn-lg">
                Start Chatting
              </Link>
              <Link href="/login" className="btn btn-outline btn-lg">
                Sign In
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-base-200">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">Powerful Features</h2>
            <p className="text-xl text-base-content/70">
              Everything you need for modern communication
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="card bg-base-100 shadow-xl">
              <div className="card-body text-center">
                <MessageCircle className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="card-title justify-center">Real-Time Messaging</h3>
                <p>Instant message delivery with WebSocket technology for seamless communication.</p>
              </div>
            </div>

            <div className="card bg-base-100 shadow-xl">
              <div className="card-body text-center">
                <Users className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="card-title justify-center">Group Chats</h3>
                <p>Create public or private rooms and invite users for group conversations.</p>
              </div>
            </div>

            <div className="card bg-base-100 shadow-xl">
              <div className="card-body text-center">
                <Shield className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="card-title justify-center">Secure & Private</h3>
                <p>End-to-end encryption and secure authentication to protect your conversations.</p>
              </div>
            </div>

            <div className="card bg-base-100 shadow-xl">
              <div className="card-body text-center">
                <Zap className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="card-title justify-center">File Sharing</h3>
                <p>Share images, documents, and files with drag-and-drop simplicity.</p>
              </div>
            </div>

            <div className="card bg-base-100 shadow-xl">
              <div className="card-body text-center">
                <Globe className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="card-title justify-center">Cross-Platform</h3>
                <p>Access your chats from any device with our responsive web application.</p>
              </div>
            </div>

            <div className="card bg-base-100 shadow-xl">
              <div className="card-body text-center">
                <Code className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="card-title justify-center">Admin Dashboard</h3>
                <p>Comprehensive admin tools for user and room management.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Tech Stack Section */}
      <section className="py-20 bg-base-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">Built with Modern Technology</h2>
            <p className="text-xl text-base-content/70">
              Powered by industry-leading frameworks and tools
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="bg-base-200 rounded-lg p-6 mb-4">
                <h3 className="font-bold text-lg">FastAPI</h3>
                <p className="text-sm text-base-content/70">Backend Framework</p>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-base-200 rounded-lg p-6 mb-4">
                <h3 className="font-bold text-lg">Next.js</h3>
                <p className="text-sm text-base-content/70">Frontend Framework</p>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-base-200 rounded-lg p-6 mb-4">
                <h3 className="font-bold text-lg">Socket.IO</h3>
                <p className="text-sm text-base-content/70">Real-time Communication</p>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-base-200 rounded-lg p-6 mb-4">
                <h3 className="font-bold text-lg">SQLAlchemy</h3>
                <p className="text-sm text-base-content/70">Database ORM</p>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-base-200 rounded-lg p-6 mb-4">
                <h3 className="font-bold text-lg">TypeScript</h3>
                <p className="text-sm text-base-content/70">Type Safety</p>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-base-200 rounded-lg p-6 mb-4">
                <h3 className="font-bold text-lg">Tailwind CSS</h3>
                <p className="text-sm text-base-content/70">Styling</p>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-base-200 rounded-lg p-6 mb-4">
                <h3 className="font-bold text-lg">DaisyUI</h3>
                <p className="text-sm text-base-content/70">UI Components</p>
              </div>
            </div>

            <div className="text-center">
              <div className="bg-base-200 rounded-lg p-6 mb-4">
                <h3 className="font-bold text-lg">SQLite</h3>
                <p className="text-sm text-base-content/70">Database</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-content">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-xl mb-8 opacity-90">
            Join thousands of users already chatting on our platform
          </p>
          <Link href="/register" className="btn btn-secondary btn-lg">
            Create Your Account
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer footer-center p-10 bg-base-200 text-base-content">
        <div>
          <div className="flex items-center space-x-2 mb-4">
            <MessageCircle className="h-6 w-6" />
            <span className="font-bold text-lg">ChatApp</span>
          </div>
          <p className="text-base-content/70">
            Built with ❤️ using FastAPI, Next.js, and modern web technologies
          </p>
          <p className="text-sm text-base-content/50">
            © 2024 ChatApp. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
