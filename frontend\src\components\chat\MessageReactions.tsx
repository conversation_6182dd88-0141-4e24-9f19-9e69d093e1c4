'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useChat } from '@/contexts/ChatContext';
import { Reaction } from '@/lib/types';

interface MessageReactionsProps {
  reactions: Reaction[];
  messageId: string;
}

export default function MessageReactions({ reactions, messageId }: MessageReactionsProps) {
  const { user } = useAuth();
  const { addReaction, removeReaction } = useChat();

  const handleReactionClick = async (emoji: string) => {
    if (!user) return;

    const reaction = reactions.find(r => r.emoji === emoji);
    const hasUserReacted = reaction?.users.includes(user.id);

    if (hasUserReacted) {
      await removeReaction(messageId, emoji);
    } else {
      await addReaction(messageId, emoji);
    }
  };

  if (reactions.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-wrap gap-1">
      {reactions.map((reaction) => {
        const hasUserReacted = user && reaction.users.includes(user.id);
        const count = reaction.users.length;

        return (
          <button
            key={reaction.id}
            onClick={() => handleReactionClick(reaction.emoji)}
            className={`
              inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs
              transition-colors border
              ${hasUserReacted 
                ? 'bg-primary/20 border-primary text-primary' 
                : 'bg-base-200 border-base-300 hover:bg-base-300'
              }
            `}
            title={`${count} reaction${count !== 1 ? 's' : ''}`}
          >
            <span>{reaction.emoji}</span>
            <span className="font-medium">{count}</span>
          </button>
        );
      })}
    </div>
  );
}
