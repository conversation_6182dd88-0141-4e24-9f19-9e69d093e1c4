@import "tailwindcss";

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Chat message animations */
.message-enter {
  opacity: 0;
  transform: translateY(10px);
}

.message-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

/* Typing indicator animation */
.typing-indicator {
  display: inline-block;
  animation: typing 1.4s infinite;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* File upload drag and drop */
.drag-over {
  border: 2px dashed #4f46e5;
  background-color: rgba(79, 70, 229, 0.1);
}

/* Message highlighting */
.highlight-message {
  background-color: rgba(79, 70, 229, 0.1);
  border-left: 3px solid #4f46e5;
  animation: highlight-fade 2s ease-out;
}

@keyframes highlight-fade {
  0% {
    background-color: rgba(79, 70, 229, 0.3);
  }
  100% {
    background-color: rgba(79, 70, 229, 0.1);
  }
}

/* Search result highlighting */
mark {
  background-color: rgba(255, 235, 59, 0.5);
  color: inherit;
  padding: 0 2px;
  border-radius: 2px;
}
