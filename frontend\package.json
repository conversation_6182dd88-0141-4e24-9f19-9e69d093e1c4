{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "echo 'Tests would run here with Jest or similar framework'", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@types/js-cookie": "^3.0.6", "axios": "^1.10.0", "clsx": "^2.1.1", "emoji-picker-react": "^4.12.2", "js-cookie": "^3.0.5", "lucide-react": "^0.522.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "socket.io-client": "^4.8.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "daisyui": "^5.0.43", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}