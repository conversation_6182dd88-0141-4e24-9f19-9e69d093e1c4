'use client';

import { useState } from 'react';
import { Plus, Hash, Lock, Settings, LogOut, User, MessageCircle, Users, MoreVertical, Info, Search, HelpCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useChat } from '@/contexts/ChatContext';
import { Room } from '@/lib/types';
import CreateRoomModal from './CreateRoomModal';
import UserSelectionModal from './UserSelectionModal';
import RoomManagementModal from './RoomManagementModal';
import BrowseRoomsModal from './BrowseRoomsModal';
import ThemeSelector from '../ui/ThemeSelector';
import HelpModal from '../ui/HelpModal';

export default function Sidebar() {
  const { user, logout } = useAuth();
  const { rooms, currentRoom, setCurrentRoom } = useChat();
  const [showCreateRoom, setShowCreateRoom] = useState(false);
  const [showUserSelection, setShowUserSelection] = useState(false);
  const [userSelectionMode, setUserSelectionMode] = useState<'direct' | 'group'>('direct');
  const [showRoomManagement, setShowRoomManagement] = useState(false);
  const [selectedRoomForManagement, setSelectedRoomForManagement] = useState<Room | null>(null);
  const [showBrowseRooms, setShowBrowseRooms] = useState(false);
  const [showHelp, setShowHelp] = useState(false);

  const handleRoomSelect = (room: Room) => {
    setCurrentRoom(room);
  };

  const handleDirectMessage = () => {
    setUserSelectionMode('direct');
    setShowUserSelection(true);
  };

  const handleGroupChat = () => {
    setUserSelectionMode('group');
    setShowUserSelection(true);
  };

  const handleRoomManagement = (room: Room, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedRoomForManagement(room);
    setShowRoomManagement(true);
  };

  return (
    <div className="h-full flex flex-col">
      {/* User info */}
      <div className="p-4 border-b border-base-300">
        <div className="flex items-center space-x-3">
          <div className="avatar">
            <div className="w-10 h-10 rounded-full bg-primary text-primary-content flex items-center justify-center">
              {user?.avatar_url ? (
                <img src={user.avatar_url} alt={user.display_name} className="w-full h-full rounded-full" />
              ) : (
                <span className="text-sm font-medium">
                  {user?.display_name?.charAt(0).toUpperCase()}
                </span>
              )}
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{user?.display_name}</p>
            <p className="text-xs text-base-content/70 truncate">@{user?.username}</p>
          </div>
          <div className="flex items-center space-x-1">
            <ThemeSelector />
            <div className="dropdown dropdown-end">
              <label tabIndex={0} className="btn btn-ghost btn-sm">
                <Settings className="h-4 w-4" />
              </label>
              <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                <li>
                  <a href="/profile" className="flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span>Profile</span>
                  </a>
                </li>
                {user?.is_admin && (
                  <li>
                    <a href="/admin" className="flex items-center space-x-2">
                      <Settings className="h-4 w-4" />
                      <span>Admin Panel</span>
                    </a>
                  </li>
                )}
                <li>
                  <button
                    onClick={() => setShowHelp(true)}
                    className="flex items-center space-x-2"
                  >
                    <HelpCircle className="h-4 w-4" />
                    <span>Keyboard Shortcuts</span>
                  </button>
                </li>
                <li>
                  <button onClick={logout} className="flex items-center space-x-2 text-error">
                    <LogOut className="h-4 w-4" />
                    <span>Logout</span>
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-4 border-b border-base-300">
        <h3 className="text-sm font-semibold text-base-content/70 uppercase tracking-wide mb-3">
          Quick Actions
        </h3>
        <div className="space-y-2">
          <button
            onClick={handleDirectMessage}
            className="btn btn-ghost btn-sm w-full justify-start gap-2"
          >
            <MessageCircle className="h-4 w-4" />
            <span>Direct Message</span>
          </button>
          <button
            onClick={handleGroupChat}
            className="btn btn-ghost btn-sm w-full justify-start gap-2"
          >
            <Users className="h-4 w-4" />
            <span>Group Chat</span>
          </button>
          <button
            onClick={() => setShowBrowseRooms(true)}
            className="btn btn-ghost btn-sm w-full justify-start gap-2"
          >
            <Search className="h-4 w-4" />
            <span>Browse Rooms</span>
          </button>
        </div>
      </div>

      {/* Rooms header */}
      <div className="p-4 border-b border-base-300">
        <div className="flex items-center justify-between">
          <h2 className="text-sm font-semibold text-base-content/70 uppercase tracking-wide">
            Rooms
          </h2>
          <button
            onClick={() => setShowCreateRoom(true)}
            className="btn btn-ghost btn-xs"
            title="Create Public Room"
          >
            <Plus className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Rooms list */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-2 space-y-1">
          {rooms.map((room) => (
            <div key={room.id} className="relative group">
              <button
                onClick={() => handleRoomSelect(room)}
                className={`
                  w-full text-left p-3 rounded-lg transition-colors
                  hover:bg-base-300 flex items-center space-x-3
                  ${currentRoom?.id === room.id ? 'bg-primary text-primary-content' : ''}
                `}
              >
                <div className="flex-shrink-0">
                  {room.is_private ? (
                    <Lock className="h-4 w-4" />
                  ) : (
                    <Hash className="h-4 w-4" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium truncate">{room.name}</p>
                    {room.unread_count > 0 && (
                      <span className="badge badge-primary badge-sm">
                        {room.unread_count}
                      </span>
                    )}
                  </div>
                  {room.last_message && (
                    <p className="text-xs opacity-70 truncate">
                      {room.last_message.sender.display_name}: {room.last_message.content}
                    </p>
                  )}
                </div>
              </button>

              {/* Room management button */}
              <button
                onClick={(e) => handleRoomManagement(room, e)}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity btn btn-ghost btn-xs"
                title="Room settings"
              >
                <Info className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Create room modal */}
      {showCreateRoom && (
        <CreateRoomModal
          isOpen={showCreateRoom}
          onClose={() => setShowCreateRoom(false)}
        />
      )}

      {/* User selection modal */}
      {showUserSelection && (
        <UserSelectionModal
          isOpen={showUserSelection}
          onClose={() => setShowUserSelection(false)}
          mode={userSelectionMode}
        />
      )}

      {/* Room management modal */}
      {showRoomManagement && selectedRoomForManagement && (
        <RoomManagementModal
          isOpen={showRoomManagement}
          onClose={() => {
            setShowRoomManagement(false);
            setSelectedRoomForManagement(null);
          }}
          room={selectedRoomForManagement}
        />
      )}

      {/* Browse rooms modal */}
      {showBrowseRooms && (
        <BrowseRoomsModal
          isOpen={showBrowseRooms}
          onClose={() => setShowBrowseRooms(false)}
        />
      )}

      {/* Help modal */}
      {showHelp && (
        <HelpModal
          isOpen={showHelp}
          onClose={() => setShowHelp(false)}
        />
      )}
    </div>
  );
}
