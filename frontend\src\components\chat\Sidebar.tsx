'use client';

import { useState } from 'react';
import { Plus, Hash, Lock, Settings, LogOut, User } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useChat } from '@/contexts/ChatContext';
import { Room } from '@/lib/types';
import CreateRoomModal from './CreateRoomModal';

export default function Sidebar() {
  const { user, logout } = useAuth();
  const { rooms, currentRoom, setCurrentRoom } = useChat();
  const [showCreateRoom, setShowCreateRoom] = useState(false);

  const handleRoomSelect = (room: Room) => {
    setCurrentRoom(room);
  };

  return (
    <div className="h-full flex flex-col">
      {/* User info */}
      <div className="p-4 border-b border-base-300">
        <div className="flex items-center space-x-3">
          <div className="avatar">
            <div className="w-10 h-10 rounded-full bg-primary text-primary-content flex items-center justify-center">
              {user?.avatar_url ? (
                <img src={user.avatar_url} alt={user.display_name} className="w-full h-full rounded-full" />
              ) : (
                <span className="text-sm font-medium">
                  {user?.display_name?.charAt(0).toUpperCase()}
                </span>
              )}
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{user?.display_name}</p>
            <p className="text-xs text-base-content/70 truncate">@{user?.username}</p>
          </div>
          <div className="dropdown dropdown-end">
            <label tabIndex={0} className="btn btn-ghost btn-sm">
              <Settings className="h-4 w-4" />
            </label>
            <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
              <li>
                <a href="/profile" className="flex items-center space-x-2">
                  <User className="h-4 w-4" />
                  <span>Profile</span>
                </a>
              </li>
              {user?.is_admin && (
                <li>
                  <a href="/admin" className="flex items-center space-x-2">
                    <Settings className="h-4 w-4" />
                    <span>Admin Panel</span>
                  </a>
                </li>
              )}
              <li>
                <button onClick={logout} className="flex items-center space-x-2 text-error">
                  <LogOut className="h-4 w-4" />
                  <span>Logout</span>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Rooms header */}
      <div className="p-4 border-b border-base-300">
        <div className="flex items-center justify-between">
          <h2 className="text-sm font-semibold text-base-content/70 uppercase tracking-wide">
            Rooms
          </h2>
          <button
            onClick={() => setShowCreateRoom(true)}
            className="btn btn-ghost btn-xs"
            title="Create Room"
          >
            <Plus className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Rooms list */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-2 space-y-1">
          {rooms.map((room) => (
            <button
              key={room.id}
              onClick={() => handleRoomSelect(room)}
              className={`
                w-full text-left p-3 rounded-lg transition-colors
                hover:bg-base-300 flex items-center space-x-3
                ${currentRoom?.id === room.id ? 'bg-primary text-primary-content' : ''}
              `}
            >
              <div className="flex-shrink-0">
                {room.is_private ? (
                  <Lock className="h-4 w-4" />
                ) : (
                  <Hash className="h-4 w-4" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium truncate">{room.name}</p>
                  {room.unread_count > 0 && (
                    <span className="badge badge-primary badge-sm">
                      {room.unread_count}
                    </span>
                  )}
                </div>
                {room.last_message && (
                  <p className="text-xs opacity-70 truncate">
                    {room.last_message.sender.display_name}: {room.last_message.content}
                  </p>
                )}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Create room modal */}
      {showCreateRoom && (
        <CreateRoomModal
          isOpen={showCreateRoom}
          onClose={() => setShowCreateRoom(false)}
        />
      )}
    </div>
  );
}
