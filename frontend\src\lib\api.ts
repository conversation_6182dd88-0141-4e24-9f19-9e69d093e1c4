import axios from 'axios';
import Cookies from 'js-cookie';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = Cookies.get('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      Cookies.remove('auth_token');
      Cookies.remove('user_data');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API calls
export const authAPI = {
  login: async (credentials: { username: string; password: string }) => {
    const response = await api.post('/users/login', credentials);
    return response.data;
  },
  
  register: async (userData: { 
    username: string; 
    email: string; 
    password: string; 
    display_name?: string;
  }) => {
    const response = await api.post('/users/register', userData);
    return response.data;
  },
  
  logout: async () => {
    const response = await api.post('/users/logout');
    Cookies.remove('auth_token');
    Cookies.remove('user_data');
    return response.data;
  },
  
  getCurrentUser: async () => {
    const response = await api.get('/users/me');
    return response.data;
  },
};

// User API calls
export const userAPI = {
  getProfile: async () => {
    const response = await api.get('/users/profile');
    return response.data;
  },
  
  updateProfile: async (profileData: {
    username?: string;
    email?: string;
    display_name?: string;
    bio?: string;
  }) => {
    const response = await api.put('/users/profile', profileData);
    return response.data;
  },
  
  changePassword: async (passwordData: {
    current_password: string;
    new_password: string;
  }) => {
    const response = await api.put('/users/change-password', passwordData);
    return response.data;
  },
  
  uploadAvatar: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    const response = await api.post('/users/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
  
  getAllUsers: async () => {
    const response = await api.get('/users/all');
    return response.data;
  },
};

// Room API calls
export const roomAPI = {
  getRooms: async () => {
    const response = await api.get('/rooms');
    return response.data;
  },
  
  createRoom: async (roomData: {
    name: string;
    description?: string;
    is_private?: boolean;
    participants?: string[];
  }) => {
    const response = await api.post('/rooms', roomData);
    return response.data;
  },
  
  joinRoom: async (roomId: string) => {
    const response = await api.post(`/rooms/${roomId}/join`);
    return response.data;
  },
  
  leaveRoom: async (roomId: string) => {
    const response = await api.post(`/rooms/${roomId}/leave`);
    return response.data;
  },
  
  getRoomMessages: async (roomId: string, page: number = 1, limit: number = 50) => {
    const response = await api.get(`/rooms/${roomId}/messages?page=${page}&limit=${limit}`);
    return response.data;
  },
};

// Message API calls
export const messageAPI = {
  sendMessage: async (messageData: {
    content: string;
    room_id?: string;
    recipient_id?: string;
    attachments?: string[];
  }) => {
    const response = await api.post('/messages', messageData);
    return response.data;
  },
  
  editMessage: async (messageId: string, content: string) => {
    const response = await api.put(`/messages/${messageId}`, { content });
    return response.data;
  },
  
  deleteMessage: async (messageId: string) => {
    const response = await api.delete(`/messages/${messageId}`);
    return response.data;
  },
  
  addReaction: async (messageId: string, emoji: string) => {
    const response = await api.post(`/messages/${messageId}/reactions`, { emoji });
    return response.data;
  },
  
  removeReaction: async (messageId: string, emoji: string) => {
    const response = await api.delete(`/messages/${messageId}/reactions/${emoji}`);
    return response.data;
  },
};

// File API calls
export const fileAPI = {
  uploadFile: async (file: File, type: 'image' | 'file' = 'file') => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    
    const response = await api.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
  
  downloadFile: async (fileId: string) => {
    const response = await api.get(`/files/${fileId}/download`, {
      responseType: 'blob',
    });
    return response.data;
  },
};

// Admin API calls
export const adminAPI = {
  getUsers: async (page: number = 1, limit: number = 20) => {
    const response = await api.get(`/admin/users?page=${page}&limit=${limit}`);
    return response.data;
  },
  
  createUser: async (userData: {
    username: string;
    email: string;
    password: string;
    display_name?: string;
    is_admin?: boolean;
  }) => {
    const response = await api.post('/admin/users', userData);
    return response.data;
  },
  
  updateUser: async (userId: string, userData: any) => {
    const response = await api.put(`/admin/users/${userId}`, userData);
    return response.data;
  },
  
  deleteUser: async (userId: string) => {
    const response = await api.delete(`/admin/users/${userId}`);
    return response.data;
  },
  
  getStats: async () => {
    const response = await api.get('/admin/stats');
    return response.data;
  },
  
  getRooms: async () => {
    const response = await api.get('/admin/rooms');
    return response.data;
  },
  
  deleteRoom: async (roomId: string) => {
    const response = await api.delete(`/admin/rooms/${roomId}`);
    return response.data;
  },
};

export default api;
