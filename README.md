# Real-Time Chat Application

A modern, feature-rich real-time chat application built with FastAPI (backend) and Next.js (frontend). This application provides comprehensive messaging capabilities with real-time communication, file sharing, user management, and administrative features.

## 🚀 Features

### Core Features
- **Real-time messaging** with WebSocket support
- **User authentication** with JWT tokens
- **Multiple chat rooms** (public and private)
- **Direct messaging** between users
- **File sharing** with image, video, and document support
- **Message reactions** with emoji support
- **Message editing and deletion**
- **Typing indicators**
- **Online status tracking**
- **Message search** functionality

### Advanced Features
- **User profile management** with avatar upload
- **Admin dashboard** for user and room management
- **Theme customization** with 25+ themes
- **Keyboard shortcuts** for power users
- **Desktop notifications**
- **Responsive design** for mobile and desktop
- **Image gallery** with zoom and navigation
- **Drag-and-drop file uploads**

## 🛠️ Tech Stack

### Backend
- **FastAPI** - Modern Python web framework
- **SQLAlchemy** - SQL toolkit and ORM
- **SQLite** - Database (easily replaceable with PostgreSQL/MySQL)
- **Socket.IO** - Real-time communication
- **JWT** - Authentication
- **Pydantic** - Data validation

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Utility-first CSS framework
- **DaisyUI** - Component library
- **Socket.IO Client** - Real-time communication
- **React Hook Form** - Form handling
- **Zod** - Schema validation

## 📦 Installation

### Prerequisites
- Python 3.8+
- Node.js 18+
- npm or yarn

### Backend Setup

1. Navigate to the backend directory:
```bash
cd backend
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Run the backend server:
```bash
uvicorn main:app --reload
```

The backend will be available at `http://localhost:8000`

### Frontend Setup

1. Navigate to the frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

The frontend will be available at `http://localhost:3000`

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file in the frontend directory:
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_SOCKET_URL=http://localhost:8000
```

### Database Configuration

The application uses SQLite by default. To use a different database, update the database URL in the backend configuration.

## 📱 Usage

### Getting Started

1. Open the application at `http://localhost:3000`
2. Register a new account or use demo credentials:
   - Username: `demo`
   - Password: `demo123`
3. Start chatting in the general room or create new rooms
4. Invite other users to private rooms
5. Share files, images, and use emoji reactions

### Keyboard Shortcuts

- `Ctrl/Cmd + K` or `/` - Open search
- `Ctrl/Cmd + B` - Toggle sidebar
- `Ctrl/Cmd + U` - Toggle user list
- `Enter` - Send message
- `Shift + Enter` - New line in message
- `Esc` - Close modals/panels

### Admin Features

Admin users can:
- View system statistics
- Manage users (create, edit, delete)
- Manage chat rooms
- Monitor system health

## 🧪 Testing

### Running Tests

Frontend tests:
```bash
cd frontend
npm test
```

Backend tests:
```bash
cd backend
pytest
```

### Test Coverage

The application includes:
- Unit tests for utility functions
- Integration tests for API endpoints
- Component tests for React components
- End-to-end tests for user workflows

## 🚀 Deployment

### Production Build

Frontend:
```bash
cd frontend
npm run build
npm start
```

Backend:
```bash
cd backend
uvicorn main:app --host 0.0.0.0 --port 8000
```

### Docker Deployment

Docker configurations are available for both frontend and backend services.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation
- Open an issue on GitHub
- Contact the development team

## 🔮 Future Enhancements

- Voice and video calling
- Message threading
- Advanced file preview
- Mobile app (React Native)
- Integration with external services
- Advanced moderation tools
- Message encryption
- Custom emoji support

---

Built with ❤️ using modern web technologies.
