'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Camera, Save, ArrowLeft, Eye, EyeOff } from 'lucide-react';
import Link from 'next/link';
import { useAuth, withAuth } from '@/contexts/AuthContext';
import { userAPI } from '@/lib/api';
import { ProfileForm, PasswordChangeForm } from '@/lib/types';
import toast from 'react-hot-toast';

const profileSchema = z.object({
  username: z
    .string()
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username must be less than 20 characters')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'),
  email: z.string().email('Please enter a valid email address'),
  display_name: z
    .string()
    .min(1, 'Display name is required')
    .max(50, 'Display name must be less than 50 characters'),
  bio: z
    .string()
    .max(200, '<PERSON><PERSON> must be less than 200 characters')
    .optional(),
});

const passwordSchema = z.object({
  current_password: z.string().min(1, 'Current password is required'),
  new_password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  confirm_password: z.string(),
}).refine((data) => data.new_password === data.confirm_password, {
  message: "Passwords don't match",
  path: ["confirm_password"],
});

function ProfilePage() {
  const { user, updateUser } = useAuth();
  const [activeTab, setActiveTab] = useState<'profile' | 'password'>('profile');
  const [uploading, setUploading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register: registerProfile,
    handleSubmit: handleSubmitProfile,
    formState: { errors: profileErrors, isSubmitting: isSubmittingProfile },
  } = useForm<ProfileForm>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      username: user?.username || '',
      email: user?.email || '',
      display_name: user?.display_name || '',
      bio: user?.bio || '',
    },
  });

  const {
    register: registerPassword,
    handleSubmit: handleSubmitPassword,
    reset: resetPassword,
    formState: { errors: passwordErrors, isSubmitting: isSubmittingPassword },
  } = useForm<PasswordChangeForm>({
    resolver: zodResolver(passwordSchema),
  });

  const onSubmitProfile = async (data: ProfileForm) => {
    try {
      const response = await userAPI.updateProfile(data);
      if (response.success) {
        updateUser(response.data);
        toast.success('Profile updated successfully');
      }
    } catch (error: any) {
      console.error('Profile update failed:', error);
      const message = error.response?.data?.message || 'Failed to update profile';
      toast.error(message);
    }
  };

  const onSubmitPassword = async (data: PasswordChangeForm) => {
    try {
      const response = await userAPI.changePassword({
        current_password: data.current_password,
        new_password: data.new_password,
      });
      if (response.success) {
        resetPassword();
        toast.success('Password changed successfully');
      }
    } catch (error: any) {
      console.error('Password change failed:', error);
      const message = error.response?.data?.message || 'Failed to change password';
      toast.error(message);
    }
  };

  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image must be less than 5MB');
      return;
    }

    try {
      setUploading(true);
      const response = await userAPI.uploadAvatar(file);
      if (response.success) {
        updateUser({ ...user!, avatar_url: response.data.avatar_url });
        toast.success('Avatar updated successfully');
      }
    } catch (error: any) {
      console.error('Avatar upload failed:', error);
      const message = error.response?.data?.message || 'Failed to upload avatar';
      toast.error(message);
    } finally {
      setUploading(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-base-100">
      {/* Header */}
      <div className="bg-base-200 border-b border-base-300">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center space-x-4">
            <Link href="/chat" className="btn btn-ghost btn-sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Chat
            </Link>
            <div>
              <h1 className="text-2xl font-bold">Profile Settings</h1>
              <p className="text-base-content/70">Manage your account settings and preferences</p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="card bg-base-200">
              <div className="card-body p-4">
                {/* Avatar */}
                <div className="text-center mb-6">
                  <div className="relative inline-block">
                    <div className="avatar">
                      <div className="w-24 h-24 rounded-full bg-primary text-primary-content flex items-center justify-center">
                        {user.avatar_url ? (
                          <img 
                            src={user.avatar_url} 
                            alt={user.display_name}
                            className="w-full h-full rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-2xl font-bold">
                            {user.display_name.charAt(0).toUpperCase()}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    {/* Upload button */}
                    <label className="absolute bottom-0 right-0 btn btn-circle btn-sm btn-primary cursor-pointer">
                      <Camera className="h-3 w-3" />
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleAvatarUpload}
                        className="hidden"
                        disabled={uploading}
                      />
                    </label>
                    
                    {uploading && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                        <div className="loading loading-spinner loading-sm text-white"></div>
                      </div>
                    )}
                  </div>
                  
                  <h3 className="font-semibold mt-3">{user.display_name}</h3>
                  <p className="text-sm text-base-content/70">@{user.username}</p>
                  {user.is_admin && (
                    <span className="badge badge-primary badge-sm mt-2">Admin</span>
                  )}
                </div>

                {/* Navigation */}
                <div className="space-y-1">
                  <button
                    onClick={() => setActiveTab('profile')}
                    className={`btn btn-ghost w-full justify-start ${
                      activeTab === 'profile' ? 'btn-active' : ''
                    }`}
                  >
                    Profile Information
                  </button>
                  <button
                    onClick={() => setActiveTab('password')}
                    className={`btn btn-ghost w-full justify-start ${
                      activeTab === 'password' ? 'btn-active' : ''
                    }`}
                  >
                    Change Password
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="card bg-base-200">
              <div className="card-body">
                {activeTab === 'profile' ? (
                  <div>
                    <h2 className="text-xl font-semibold mb-6">Profile Information</h2>
                    
                    <form onSubmit={handleSubmitProfile(onSubmitProfile)} className="space-y-6">
                      {/* Username */}
                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">Username</span>
                        </label>
                        <input
                          type="text"
                          className={`input input-bordered ${
                            profileErrors.username ? 'input-error' : ''
                          }`}
                          {...registerProfile('username')}
                        />
                        {profileErrors.username && (
                          <label className="label">
                            <span className="label-text-alt text-error">
                              {profileErrors.username.message}
                            </span>
                          </label>
                        )}
                      </div>

                      {/* Email */}
                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">Email</span>
                        </label>
                        <input
                          type="email"
                          className={`input input-bordered ${
                            profileErrors.email ? 'input-error' : ''
                          }`}
                          {...registerProfile('email')}
                        />
                        {profileErrors.email && (
                          <label className="label">
                            <span className="label-text-alt text-error">
                              {profileErrors.email.message}
                            </span>
                          </label>
                        )}
                      </div>

                      {/* Display Name */}
                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">Display Name</span>
                        </label>
                        <input
                          type="text"
                          className={`input input-bordered ${
                            profileErrors.display_name ? 'input-error' : ''
                          }`}
                          {...registerProfile('display_name')}
                        />
                        {profileErrors.display_name && (
                          <label className="label">
                            <span className="label-text-alt text-error">
                              {profileErrors.display_name.message}
                            </span>
                          </label>
                        )}
                      </div>

                      {/* Bio */}
                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">Bio</span>
                        </label>
                        <textarea
                          className={`textarea textarea-bordered ${
                            profileErrors.bio ? 'textarea-error' : ''
                          }`}
                          rows={3}
                          placeholder="Tell us about yourself..."
                          {...registerProfile('bio')}
                        />
                        {profileErrors.bio && (
                          <label className="label">
                            <span className="label-text-alt text-error">
                              {profileErrors.bio.message}
                            </span>
                          </label>
                        )}
                      </div>

                      {/* Submit Button */}
                      <button
                        type="submit"
                        disabled={isSubmittingProfile}
                        className={`btn btn-primary ${
                          isSubmittingProfile ? 'loading' : ''
                        }`}
                      >
                        {isSubmittingProfile ? (
                          'Saving...'
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Save Changes
                          </>
                        )}
                      </button>
                    </form>
                  </div>
                ) : (
                  <div>
                    <h2 className="text-xl font-semibold mb-6">Change Password</h2>
                    
                    <form onSubmit={handleSubmitPassword(onSubmitPassword)} className="space-y-6">
                      {/* Current Password */}
                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">Current Password</span>
                        </label>
                        <div className="relative">
                          <input
                            type={showCurrentPassword ? 'text' : 'password'}
                            className={`input input-bordered w-full pr-12 ${
                              passwordErrors.current_password ? 'input-error' : ''
                            }`}
                            {...registerPassword('current_password')}
                          />
                          <button
                            type="button"
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                          >
                            {showCurrentPassword ? (
                              <EyeOff className="h-5 w-5 text-base-content/50" />
                            ) : (
                              <Eye className="h-5 w-5 text-base-content/50" />
                            )}
                          </button>
                        </div>
                        {passwordErrors.current_password && (
                          <label className="label">
                            <span className="label-text-alt text-error">
                              {passwordErrors.current_password.message}
                            </span>
                          </label>
                        )}
                      </div>

                      {/* New Password */}
                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">New Password</span>
                        </label>
                        <div className="relative">
                          <input
                            type={showNewPassword ? 'text' : 'password'}
                            className={`input input-bordered w-full pr-12 ${
                              passwordErrors.new_password ? 'input-error' : ''
                            }`}
                            {...registerPassword('new_password')}
                          />
                          <button
                            type="button"
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                          >
                            {showNewPassword ? (
                              <EyeOff className="h-5 w-5 text-base-content/50" />
                            ) : (
                              <Eye className="h-5 w-5 text-base-content/50" />
                            )}
                          </button>
                        </div>
                        {passwordErrors.new_password && (
                          <label className="label">
                            <span className="label-text-alt text-error">
                              {passwordErrors.new_password.message}
                            </span>
                          </label>
                        )}
                      </div>

                      {/* Confirm Password */}
                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">Confirm New Password</span>
                        </label>
                        <div className="relative">
                          <input
                            type={showConfirmPassword ? 'text' : 'password'}
                            className={`input input-bordered w-full pr-12 ${
                              passwordErrors.confirm_password ? 'input-error' : ''
                            }`}
                            {...registerPassword('confirm_password')}
                          />
                          <button
                            type="button"
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          >
                            {showConfirmPassword ? (
                              <EyeOff className="h-5 w-5 text-base-content/50" />
                            ) : (
                              <Eye className="h-5 w-5 text-base-content/50" />
                            )}
                          </button>
                        </div>
                        {passwordErrors.confirm_password && (
                          <label className="label">
                            <span className="label-text-alt text-error">
                              {passwordErrors.confirm_password.message}
                            </span>
                          </label>
                        )}
                      </div>

                      {/* Submit Button */}
                      <button
                        type="submit"
                        disabled={isSubmittingPassword}
                        className={`btn btn-primary ${
                          isSubmittingPassword ? 'loading' : ''
                        }`}
                      >
                        {isSubmittingPassword ? (
                          'Changing Password...'
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Change Password
                          </>
                        )}
                      </button>
                    </form>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default withAuth(ProfilePage);
