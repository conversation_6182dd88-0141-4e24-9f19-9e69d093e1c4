'use client';

import { useState } from 'react';
import { X, <PERSON>ting<PERSON>, UserMinus, LogOut, Users, Hash, Lock, Calendar } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useChat } from '@/contexts/ChatContext';
import { Room } from '@/lib/types';
import { formatDate } from '@/lib/utils';
import toast from 'react-hot-toast';

interface RoomManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  room: Room;
}

export default function RoomManagementModal({ isOpen, onClose, room }: RoomManagementModalProps) {
  const { user } = useAuth();
  const { leaveRoom, setCurrentRoom } = useChat();
  const [loading, setLoading] = useState(false);

  const isRoomCreator = room.created_by === user?.id;
  const isParticipant = room.participants.some(p => p.id === user?.id);

  const handleLeaveRoom = async () => {
    if (!confirm('Are you sure you want to leave this room?')) {
      return;
    }

    try {
      setLoading(true);
      await leaveRoom(room.id);
      
      // If this was the current room, clear it
      setCurrentRoom(null);
      
      toast.success('Left room successfully');
      onClose();
    } catch (error) {
      console.error('Failed to leave room:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-base-100 rounded-lg shadow-xl w-full max-w-lg max-h-[80vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-base-300">
          <div className="flex items-center space-x-3">
            {room.is_private ? (
              <Lock className="h-6 w-6 text-primary" />
            ) : (
              <Hash className="h-6 w-6 text-primary" />
            )}
            <div>
              <h2 className="text-xl font-semibold">{room.name}</h2>
              <p className="text-sm text-base-content/70">
                {room.is_private ? 'Private Room' : 'Public Room'}
              </p>
            </div>
          </div>
          <button onClick={onClose} className="btn btn-ghost btn-sm">
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Room Details */}
        <div className="p-6 space-y-6">
          {/* Description */}
          {room.description && (
            <div>
              <h3 className="font-semibold mb-2">Description</h3>
              <p className="text-base-content/80">{room.description}</p>
            </div>
          )}

          {/* Room Info */}
          <div>
            <h3 className="font-semibold mb-3">Room Information</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-base-content/50" />
                <span className="text-sm">Created {formatDate(room.created_at)}</span>
              </div>
              <div className="flex items-center space-x-3">
                <Users className="h-4 w-4 text-base-content/50" />
                <span className="text-sm">{room.participants.length} participants</span>
              </div>
            </div>
          </div>

          {/* Participants */}
          <div>
            <h3 className="font-semibold mb-3">Participants ({room.participants.length})</h3>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {room.participants.map(participant => (
                <div key={participant.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-base-200">
                  {/* Avatar */}
                  <div className="avatar">
                    <div className="w-8 h-8 rounded-full bg-primary text-primary-content flex items-center justify-center">
                      {participant.avatar_url ? (
                        <img 
                          src={participant.avatar_url} 
                          alt={participant.display_name}
                          className="w-full h-full rounded-full"
                        />
                      ) : (
                        <span className="text-xs font-medium">
                          {participant.display_name.charAt(0).toUpperCase()}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* User info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium truncate">
                        {participant.display_name}
                        {participant.id === user?.id && (
                          <span className="text-xs text-base-content/50 ml-1">(You)</span>
                        )}
                      </p>
                      {participant.is_admin && (
                        <span className="badge badge-primary badge-xs">Admin</span>
                      )}
                      {participant.id === room.created_by && (
                        <span className="badge badge-secondary badge-xs">Creator</span>
                      )}
                    </div>
                    <p className="text-xs text-base-content/70 truncate">@{participant.username}</p>
                  </div>

                  {/* Status */}
                  <div className={`w-2 h-2 rounded-full ${participant.is_online ? 'bg-success' : 'bg-base-300'}`} />
                </div>
              ))}
            </div>
          </div>

          {/* Last Message */}
          {room.last_message && (
            <div>
              <h3 className="font-semibold mb-2">Last Message</h3>
              <div className="bg-base-200 p-3 rounded-lg">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="text-sm font-medium">{room.last_message.sender.display_name}</span>
                  <span className="text-xs text-base-content/50">
                    {formatDate(room.last_message.created_at)}
                  </span>
                </div>
                <p className="text-sm text-base-content/80">{room.last_message.content}</p>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        {isParticipant && (
          <div className="p-6 border-t border-base-300">
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="btn btn-ghost flex-1"
              >
                Close
              </button>
              
              {!isRoomCreator && (
                <button
                  onClick={handleLeaveRoom}
                  disabled={loading}
                  className={`btn btn-error flex-1 ${loading ? 'loading' : ''}`}
                >
                  {loading ? (
                    'Leaving...'
                  ) : (
                    <>
                      <LogOut className="h-4 w-4 mr-2" />
                      Leave Room
                    </>
                  )}
                </button>
              )}
            </div>
            
            {isRoomCreator && (
              <div className="mt-3">
                <div className="alert alert-info">
                  <div className="text-sm">
                    <p className="font-medium">You are the creator of this room</p>
                    <p>You cannot leave this room. Contact an admin if you need to delete it.</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
