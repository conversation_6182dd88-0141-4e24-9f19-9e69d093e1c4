import { io, Socket } from 'socket.io-client';
import Cookies from 'js-cookie';

class SocketManager {
  private socket: Socket | null = null;
  private userId: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  connect(userId: string) {
    if (this.socket?.connected) {
      return this.socket;
    }

    this.userId = userId;
    const token = Cookies.get('auth_token');
    
    const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:8000';
    
    this.socket = io(socketUrl, {
      auth: {
        token,
        userId,
      },
      transports: ['websocket', 'polling'],
      upgrade: true,
      rememberUpgrade: true,
    });

    this.setupEventListeners();
    return this.socket;
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Connected to server');
      this.reconnectAttempts = 0;
      
      // Join user to their personal room for direct messages
      if (this.userId) {
        this.socket?.emit('join_user_room', this.userId);
      }
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from server:', reason);
      
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.handleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      this.handleReconnect();
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.log('Reconnected after', attemptNumber, 'attempts');
      this.reconnectAttempts = 0;
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('Reconnection error:', error);
    });
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.socket?.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  // Message events
  sendMessage(messageData: {
    content: string;
    room_id?: string;
    recipient_id?: string;
    attachments?: any[];
  }) {
    if (this.socket?.connected) {
      this.socket.emit('send_message', messageData);
    }
  }

  editMessage(messageId: string, content: string) {
    if (this.socket?.connected) {
      this.socket.emit('edit_message', { messageId, content });
    }
  }

  deleteMessage(messageId: string) {
    if (this.socket?.connected) {
      this.socket.emit('delete_message', { messageId });
    }
  }

  // Room events
  joinRoom(roomId: string) {
    if (this.socket?.connected) {
      this.socket.emit('join_room', roomId);
    }
  }

  leaveRoom(roomId: string) {
    if (this.socket?.connected) {
      this.socket.emit('leave_room', roomId);
    }
  }

  // Typing events
  startTyping(roomId: string) {
    if (this.socket?.connected) {
      this.socket.emit('typing_start', { roomId });
    }
  }

  stopTyping(roomId: string) {
    if (this.socket?.connected) {
      this.socket.emit('typing_stop', { roomId });
    }
  }

  // Reaction events
  addReaction(messageId: string, emoji: string) {
    if (this.socket?.connected) {
      this.socket.emit('add_reaction', { messageId, emoji });
    }
  }

  removeReaction(messageId: string, emoji: string) {
    if (this.socket?.connected) {
      this.socket.emit('remove_reaction', { messageId, emoji });
    }
  }

  // Event listeners
  onMessage(callback: (message: any) => void) {
    this.socket?.on('new_message', callback);
  }

  onMessageEdit(callback: (data: { messageId: string; content: string }) => void) {
    this.socket?.on('message_edited', callback);
  }

  onMessageDelete(callback: (data: { messageId: string }) => void) {
    this.socket?.on('message_deleted', callback);
  }

  onUserJoined(callback: (user: any) => void) {
    this.socket?.on('user_joined', callback);
  }

  onUserLeft(callback: (user: any) => void) {
    this.socket?.on('user_left', callback);
  }

  onTypingStart(callback: (data: { userId: string; roomId: string; username: string }) => void) {
    this.socket?.on('typing_start', callback);
  }

  onTypingStop(callback: (data: { userId: string; roomId: string }) => void) {
    this.socket?.on('typing_stop', callback);
  }

  onReactionAdded(callback: (data: { messageId: string; emoji: string; userId: string }) => void) {
    this.socket?.on('reaction_added', callback);
  }

  onReactionRemoved(callback: (data: { messageId: string; emoji: string; userId: string }) => void) {
    this.socket?.on('reaction_removed', callback);
  }

  onUserStatusChange(callback: (data: { userId: string; status: 'online' | 'offline' }) => void) {
    this.socket?.on('user_status_change', callback);
  }

  // Remove event listeners
  off(event: string, callback?: (...args: any[]) => void) {
    this.socket?.off(event, callback);
  }

  // Disconnect
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.userId = null;
      this.reconnectAttempts = 0;
    }
  }

  // Get connection status
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Get socket instance
  getSocket(): Socket | null {
    return this.socket;
  }
}

// Create singleton instance
const socketManager = new SocketManager();
export default socketManager;
