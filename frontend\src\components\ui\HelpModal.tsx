'use client';

import { X, Keyboard, Search, MessageSquare, Users, Sidebar } from 'lucide-react';

interface HelpModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const shortcuts = [
  {
    category: 'Navigation',
    icon: <Sidebar className="h-4 w-4" />,
    items: [
      { keys: ['Ctrl', 'B'], description: 'Toggle sidebar' },
      { keys: ['Ctrl', 'U'], description: 'Toggle user list' },
      { keys: ['Esc'], description: 'Close modals/panels' },
    ],
  },
  {
    category: 'Search',
    icon: <Search className="h-4 w-4" />,
    items: [
      { keys: ['Ctrl', 'K'], description: 'Open search' },
      { keys: ['Ctrl', 'F'], description: 'Open search' },
      { keys: ['/'], description: 'Quick search' },
    ],
  },
  {
    category: 'Messaging',
    icon: <MessageSquare className="h-4 w-4" />,
    items: [
      { keys: ['Enter'], description: 'Send message' },
      { keys: ['Shift', 'Enter'], description: 'New line in message' },
      { keys: ['Ctrl', 'N'], description: 'New message/room' },
    ],
  },
];

export default function HelpModal({ isOpen, onClose }: HelpModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-base-100 rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-base-300">
          <div className="flex items-center space-x-2">
            <Keyboard className="h-6 w-6 text-primary" />
            <h2 className="text-xl font-semibold">Keyboard Shortcuts</h2>
          </div>
          <button onClick={onClose} className="btn btn-ghost btn-sm">
            <X className="h-4 w-4" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {shortcuts.map((category) => (
              <div key={category.category}>
                <div className="flex items-center space-x-2 mb-4">
                  {category.icon}
                  <h3 className="font-semibold text-lg">{category.category}</h3>
                </div>
                
                <div className="space-y-3">
                  {category.items.map((shortcut, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-base-content/80">
                        {shortcut.description}
                      </span>
                      <div className="flex items-center space-x-1">
                        {shortcut.keys.map((key, keyIndex) => (
                          <div key={keyIndex} className="flex items-center">
                            <kbd className="kbd kbd-sm">{key}</kbd>
                            {keyIndex < shortcut.keys.length - 1 && (
                              <span className="mx-1 text-base-content/50">+</span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Additional Tips */}
          <div className="mt-8 p-4 bg-base-200 rounded-lg">
            <h4 className="font-semibold mb-3">Tips</h4>
            <ul className="space-y-2 text-sm text-base-content/80">
              <li>• Use <kbd className="kbd kbd-sm">@username</kbd> to mention someone in a message</li>
              <li>• Drag and drop files directly into the chat to share them</li>
              <li>• Click on images to view them in full screen</li>
              <li>• Use emoji reactions by hovering over messages</li>
              <li>• Right-click messages for more options (edit, delete, etc.)</li>
            </ul>
          </div>

          {/* Browser Support */}
          <div className="mt-6 text-xs text-base-content/50">
            <p>
              Some shortcuts may vary depending on your operating system. 
              On Mac, use <kbd className="kbd kbd-sm">Cmd</kbd> instead of <kbd className="kbd kbd-sm">Ctrl</kbd>.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-base-300">
          <button onClick={onClose} className="btn btn-primary w-full">
            Got it!
          </button>
        </div>
      </div>
    </div>
  );
}
