'use client';

import { useState, useEffect } from 'react';
import { Search, X, MessageCircle, Users } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useChat } from '@/contexts/ChatContext';
import { userAPI } from '@/lib/api';
import { User } from '@/lib/types';
import toast from 'react-hot-toast';

interface UserSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'direct' | 'group';
}

export default function UserSelectionModal({ isOpen, onClose, mode }: UserSelectionModalProps) {
  const { user: currentUser } = useAuth();
  const { createRoom, setCurrentRoom } = useChat();
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadUsers();
    }
  }, [isOpen]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getAllUsers();
      if (response.success) {
        // Filter out current user
        const filteredUsers = response.data.filter((u: User) => u.id !== currentUser?.id);
        setUsers(filteredUsers);
      }
    } catch (error) {
      console.error('Failed to load users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter(user =>
    user.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleUserToggle = (user: User) => {
    setSelectedUsers(prev => {
      const isSelected = prev.find(u => u.id === user.id);
      if (isSelected) {
        return prev.filter(u => u.id !== user.id);
      } else {
        if (mode === 'direct' && prev.length >= 1) {
          // For direct messages, only allow one user
          return [user];
        }
        return [...prev, user];
      }
    });
  };

  const handleCreate = async () => {
    if (selectedUsers.length === 0) {
      toast.error('Please select at least one user');
      return;
    }

    try {
      setCreating(true);

      if (mode === 'direct') {
        // Create direct message room
        const selectedUser = selectedUsers[0];
        const roomName = `${currentUser?.display_name} & ${selectedUser.display_name}`;
        
        const room = await createRoom({
          name: roomName,
          description: `Direct message between ${currentUser?.display_name} and ${selectedUser.display_name}`,
          is_private: true,
          participants: [selectedUser.id],
        });

        // Switch to the new room
        setCurrentRoom(room);
        toast.success(`Started conversation with ${selectedUser.display_name}`);
      } else {
        // Create group room
        const userNames = selectedUsers.map(u => u.display_name).join(', ');
        const roomName = `Group: ${userNames.substring(0, 30)}${userNames.length > 30 ? '...' : ''}`;
        
        const room = await createRoom({
          name: roomName,
          description: `Group chat with ${selectedUsers.length} members`,
          is_private: true,
          participants: selectedUsers.map(u => u.id),
        });

        // Switch to the new room
        setCurrentRoom(room);
        toast.success(`Created group chat with ${selectedUsers.length} members`);
      }

      handleClose();
    } catch (error) {
      console.error('Failed to create room:', error);
    } finally {
      setCreating(false);
    }
  };

  const handleClose = () => {
    setSelectedUsers([]);
    setSearchTerm('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-base-100 rounded-lg shadow-xl w-full max-w-md max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-base-300">
          <div className="flex items-center space-x-2">
            {mode === 'direct' ? (
              <MessageCircle className="h-5 w-5 text-primary" />
            ) : (
              <Users className="h-5 w-5 text-primary" />
            )}
            <h2 className="text-xl font-semibold">
              {mode === 'direct' ? 'Start Direct Message' : 'Create Group Chat'}
            </h2>
          </div>
          <button onClick={handleClose} className="btn btn-ghost btn-sm">
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Search */}
        <div className="p-4 border-b border-base-300">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-base-content/50" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input input-bordered w-full pl-10"
            />
          </div>
        </div>

        {/* Selected Users */}
        {selectedUsers.length > 0 && (
          <div className="p-4 border-b border-base-300">
            <p className="text-sm font-medium text-base-content/70 mb-2">
              Selected ({selectedUsers.length}):
            </p>
            <div className="flex flex-wrap gap-2">
              {selectedUsers.map(user => (
                <div
                  key={user.id}
                  className="flex items-center space-x-2 bg-primary/10 text-primary px-3 py-1 rounded-full text-sm"
                >
                  <span>{user.display_name}</span>
                  <button
                    onClick={() => handleUserToggle(user)}
                    className="hover:bg-primary/20 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Users List */}
        <div className="flex-1 overflow-y-auto p-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="loading loading-spinner loading-md"></div>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8 text-base-content/50">
              {searchTerm ? 'No users found' : 'No users available'}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredUsers.map(user => {
                const isSelected = selectedUsers.find(u => u.id === user.id);
                
                return (
                  <button
                    key={user.id}
                    onClick={() => handleUserToggle(user)}
                    className={`
                      w-full flex items-center space-x-3 p-3 rounded-lg transition-colors text-left
                      ${isSelected 
                        ? 'bg-primary/10 border border-primary' 
                        : 'hover:bg-base-200 border border-transparent'
                      }
                    `}
                  >
                    {/* Avatar */}
                    <div className="relative">
                      <div className="avatar">
                        <div className="w-10 h-10 rounded-full bg-primary text-primary-content flex items-center justify-center">
                          {user.avatar_url ? (
                            <img 
                              src={user.avatar_url} 
                              alt={user.display_name}
                              className="w-full h-full rounded-full"
                            />
                          ) : (
                            <span className="text-sm font-medium">
                              {user.display_name.charAt(0).toUpperCase()}
                            </span>
                          )}
                        </div>
                      </div>
                      
                      {/* Online indicator */}
                      <div className={`
                        absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-base-100
                        ${user.is_online ? 'bg-success' : 'bg-base-300'}
                      `} />
                    </div>

                    {/* User info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="font-medium truncate">{user.display_name}</p>
                        {user.is_admin && (
                          <span className="badge badge-primary badge-xs">Admin</span>
                        )}
                      </div>
                      <p className="text-sm text-base-content/70 truncate">@{user.username}</p>
                      {user.bio && (
                        <p className="text-xs text-base-content/50 truncate">{user.bio}</p>
                      )}
                    </div>

                    {/* Selection indicator */}
                    {isSelected && (
                      <div className="flex-shrink-0">
                        <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                          <svg className="w-3 h-3 text-primary-content" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    )}
                  </button>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-base-300">
          <div className="flex space-x-3">
            <button
              onClick={handleClose}
              className="btn btn-ghost flex-1"
              disabled={creating}
            >
              Cancel
            </button>
            <button
              onClick={handleCreate}
              disabled={selectedUsers.length === 0 || creating}
              className={`btn btn-primary flex-1 ${creating ? 'loading' : ''}`}
            >
              {creating 
                ? 'Creating...' 
                : mode === 'direct' 
                  ? 'Start Chat' 
                  : `Create Group (${selectedUsers.length})`
              }
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
