'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import toast from 'react-hot-toast';
import { useAuth } from './AuthContext';
import socketManager from '@/lib/socket';
import { roomAPI, messageAPI, fileAPI } from '@/lib/api';
import { showMessageNotification, requestNotificationPermission } from '@/lib/notifications';
import { 
  Room, 
  Message, 
  User, 
  TypingIndicator, 
  ChatContextType, 
  CreateRoomForm 
} from '@/lib/types';

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export function ChatProvider({ children }: { children: React.ReactNode }) {
  const { user, isAuthenticated } = useAuth();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [currentRoom, setCurrentRoom] = useState<Room | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<User[]>([]);
  const [typingUsers, setTypingUsers] = useState<TypingIndicator[]>([]);
  const [loading, setLoading] = useState(false);

  // Initialize socket connection when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      const socket = socketManager.connect(user.id);
      setupSocketListeners();
      loadRooms();

      // Request notification permission
      requestNotificationPermission();

      return () => {
        socketManager.disconnect();
      };
    }
  }, [isAuthenticated, user]);

  const setupSocketListeners = useCallback(() => {
    // Message events
    socketManager.onMessage((message: Message) => {
      setMessages(prev => [...prev, message]);

      // Update room's last message
      setRooms(prev => prev.map(room =>
        room.id === message.room_id
          ? { ...room, last_message: message }
          : room
      ));

      // Show notification for messages from other users
      if (message.sender_id !== user?.id) {
        const room = rooms.find(r => r.id === message.room_id);
        const isCurrentRoom = currentRoom?.id === message.room_id;

        // Only show notification if not in the current room or window is not focused
        if (!isCurrentRoom || !document.hasFocus()) {
          showMessageNotification(
            message.sender.display_name,
            message.content,
            room?.name,
            () => {
              // Focus window and switch to room when notification is clicked
              window.focus();
              if (room) {
                setCurrentRoom(room);
              }
            }
          );
        }
      }
    });

    socketManager.onMessageEdit(({ messageId, content }) => {
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, content, is_edited: true }
          : msg
      ));
    });

    socketManager.onMessageDelete(({ messageId }) => {
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
    });

    // User events
    socketManager.onUserJoined((user: User) => {
      setOnlineUsers(prev => {
        if (!prev.find(u => u.id === user.id)) {
          return [...prev, user];
        }
        return prev;
      });
      toast.success(`${user.display_name} joined the chat`);
    });

    socketManager.onUserLeft((user: User) => {
      setOnlineUsers(prev => prev.filter(u => u.id !== user.id));
      toast(`${user.display_name} left the chat`, { icon: '👋' });
    });

    // Typing events
    socketManager.onTypingStart(({ userId, roomId, username }) => {
      if (userId !== user?.id && roomId === currentRoom?.id) {
        setTypingUsers(prev => {
          if (!prev.find(t => t.userId === userId)) {
            return [...prev, { userId, roomId, username }];
          }
          return prev;
        });
      }
    });

    socketManager.onTypingStop(({ userId }) => {
      setTypingUsers(prev => prev.filter(t => t.userId !== userId));
    });

    // Reaction events
    socketManager.onReactionAdded(({ messageId, emoji, userId }) => {
      setMessages(prev => prev.map(msg => {
        if (msg.id === messageId) {
          const existingReaction = msg.reactions.find(r => r.emoji === emoji);
          if (existingReaction) {
            // Add user to existing reaction
            return {
              ...msg,
              reactions: msg.reactions.map(r => 
                r.emoji === emoji 
                  ? { ...r, users: [...r.users, userId] }
                  : r
              )
            };
          } else {
            // Create new reaction
            return {
              ...msg,
              reactions: [...msg.reactions, {
                id: `${messageId}-${emoji}`,
                emoji,
                users: [userId],
                message_id: messageId,
                created_at: new Date().toISOString()
              }]
            };
          }
        }
        return msg;
      }));
    });

    socketManager.onReactionRemoved(({ messageId, emoji, userId }) => {
      setMessages(prev => prev.map(msg => {
        if (msg.id === messageId) {
          return {
            ...msg,
            reactions: msg.reactions.map(r => 
              r.emoji === emoji 
                ? { ...r, users: r.users.filter(u => u !== userId) }
                : r
            ).filter(r => r.users.length > 0)
          };
        }
        return msg;
      }));
    });

    // Status events
    socketManager.onUserStatusChange(({ userId, status }) => {
      setOnlineUsers(prev => {
        if (status === 'online') {
          // Add user if not already in list
          const existingUser = prev.find(u => u.id === userId);
          if (!existingUser) {
            // You might want to fetch user data here
            return prev;
          }
          return prev;
        } else {
          // Remove user from online list
          return prev.filter(u => u.id !== userId);
        }
      });
    });
  }, [user, currentRoom]);

  const loadRooms = async () => {
    try {
      setLoading(true);
      const response = await roomAPI.getRooms();
      if (response.success) {
        setRooms(response.data);
        
        // Auto-select first room or general room
        const generalRoom = response.data.find((room: Room) => 
          room.name.toLowerCase() === 'general'
        );
        const firstRoom = generalRoom || response.data[0];
        
        if (firstRoom) {
          await selectRoom(firstRoom);
        }
      }
    } catch (error) {
      console.error('Failed to load rooms:', error);
      toast.error('Failed to load chat rooms');
    } finally {
      setLoading(false);
    }
  };

  const selectRoom = async (room: Room) => {
    try {
      setCurrentRoom(room);
      setMessages([]);
      setTypingUsers([]);
      
      // Join the room
      socketManager.joinRoom(room.id);
      
      // Load room messages
      const response = await roomAPI.getRoomMessages(room.id);
      if (response.success) {
        setMessages(response.data);
      }
    } catch (error) {
      console.error('Failed to select room:', error);
      toast.error('Failed to load room messages');
    }
  };

  const sendMessage = async (content: string, attachments?: File[]) => {
    if (!currentRoom || !user) return;

    try {
      let attachmentUrls: string[] = [];
      
      // Upload attachments if any
      if (attachments && attachments.length > 0) {
        const uploadPromises = attachments.map(file => 
          fileAPI.uploadFile(file, file.type.startsWith('image/') ? 'image' : 'file')
        );
        
        const uploadResults = await Promise.all(uploadPromises);
        attachmentUrls = uploadResults
          .filter(result => result.success)
          .map(result => result.data.url);
      }

      // Send message via socket
      socketManager.sendMessage({
        content,
        room_id: currentRoom.id,
        attachments: attachmentUrls,
      });

    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
    }
  };

  const editMessage = async (messageId: string, content: string) => {
    try {
      socketManager.editMessage(messageId, content);
    } catch (error) {
      console.error('Failed to edit message:', error);
      toast.error('Failed to edit message');
    }
  };

  const deleteMessage = async (messageId: string) => {
    try {
      socketManager.deleteMessage(messageId);
    } catch (error) {
      console.error('Failed to delete message:', error);
      toast.error('Failed to delete message');
    }
  };

  const addReaction = async (messageId: string, emoji: string) => {
    try {
      socketManager.addReaction(messageId, emoji);
    } catch (error) {
      console.error('Failed to add reaction:', error);
      toast.error('Failed to add reaction');
    }
  };

  const removeReaction = async (messageId: string, emoji: string) => {
    try {
      socketManager.removeReaction(messageId, emoji);
    } catch (error) {
      console.error('Failed to remove reaction:', error);
      toast.error('Failed to remove reaction');
    }
  };

  const joinRoom = async (roomId: string) => {
    try {
      await roomAPI.joinRoom(roomId);
      socketManager.joinRoom(roomId);
      await loadRooms(); // Refresh rooms list
    } catch (error) {
      console.error('Failed to join room:', error);
      toast.error('Failed to join room');
    }
  };

  const leaveRoom = async (roomId: string) => {
    try {
      await roomAPI.leaveRoom(roomId);
      socketManager.leaveRoom(roomId);
      await loadRooms(); // Refresh rooms list
    } catch (error) {
      console.error('Failed to leave room:', error);
      toast.error('Failed to leave room');
    }
  };

  const createRoom = async (roomData: CreateRoomForm): Promise<Room> => {
    try {
      const response = await roomAPI.createRoom(roomData);
      if (response.success) {
        await loadRooms(); // Refresh rooms list
        toast.success('Room created successfully');
        return response.data;
      }
      throw new Error(response.message || 'Failed to create room');
    } catch (error) {
      console.error('Failed to create room:', error);
      toast.error('Failed to create room');
      throw error;
    }
  };

  const value: ChatContextType = {
    rooms,
    currentRoom,
    messages,
    onlineUsers,
    typingUsers,
    setCurrentRoom: selectRoom,
    sendMessage,
    editMessage,
    deleteMessage,
    addReaction,
    removeReaction,
    joinRoom,
    leaveRoom,
    createRoom,
    loading,
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
}

export function useChat() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
}
