'use client';

import { useState } from 'react';
import { ChevronLeft, ChevronRight, X, Download, ZoomIn, ZoomOut } from 'lucide-react';
import { Attachment } from '@/lib/types';
import { formatFileSize } from '@/lib/utils';
import { fileAPI } from '@/lib/api';
import toast from 'react-hot-toast';

interface ImageGalleryProps {
  images: Attachment[];
  initialIndex?: number;
  isOpen: boolean;
  onClose: () => void;
}

export default function ImageGallery({ images, initialIndex = 0, isOpen, onClose }: ImageGalleryProps) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [zoom, setZoom] = useState(1);
  const [downloading, setDownloading] = useState(false);

  if (!isOpen || images.length === 0) return null;

  const currentImage = images[currentIndex];

  const goToPrevious = () => {
    setCurrentIndex(prev => (prev > 0 ? prev - 1 : images.length - 1));
    setZoom(1);
  };

  const goToNext = () => {
    setCurrentIndex(prev => (prev < images.length - 1 ? prev + 1 : 0));
    setZoom(1);
  };

  const handleDownload = async () => {
    try {
      setDownloading(true);
      const blob = await fileAPI.downloadFile(currentImage.id);
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = currentImage.original_filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success('Image downloaded successfully');
    } catch (error) {
      console.error('Download failed:', error);
      toast.error('Failed to download image');
    } finally {
      setDownloading(false);
    }
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.25, 0.25));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'Escape':
        onClose();
        break;
      case 'ArrowLeft':
        goToPrevious();
        break;
      case 'ArrowRight':
        goToNext();
        break;
      case '+':
      case '=':
        handleZoomIn();
        break;
      case '-':
        handleZoomOut();
        break;
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50"
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 bg-black bg-opacity-50 p-4 flex items-center justify-between z-10">
        <div className="text-white">
          <h3 className="font-medium">{currentImage.original_filename}</h3>
          <p className="text-sm opacity-75">
            {currentIndex + 1} of {images.length} • {formatFileSize(currentImage.file_size)}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Zoom controls */}
          <button
            onClick={handleZoomOut}
            className="btn btn-ghost btn-sm text-white"
            title="Zoom out"
          >
            <ZoomOut className="h-4 w-4" />
          </button>
          <span className="text-white text-sm min-w-[3rem] text-center">
            {Math.round(zoom * 100)}%
          </span>
          <button
            onClick={handleZoomIn}
            className="btn btn-ghost btn-sm text-white"
            title="Zoom in"
          >
            <ZoomIn className="h-4 w-4" />
          </button>
          
          {/* Download button */}
          <button
            onClick={handleDownload}
            disabled={downloading}
            className="btn btn-ghost btn-sm text-white"
            title="Download"
          >
            {downloading ? (
              <div className="loading loading-spinner loading-xs"></div>
            ) : (
              <Download className="h-4 w-4" />
            )}
          </button>
          
          {/* Close button */}
          <button
            onClick={onClose}
            className="btn btn-ghost btn-sm text-white"
            title="Close (Esc)"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Navigation arrows */}
      {images.length > 1 && (
        <>
          <button
            onClick={goToPrevious}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 btn btn-circle btn-lg bg-black bg-opacity-50 text-white border-none hover:bg-opacity-70 z-10"
            title="Previous (←)"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
          
          <button
            onClick={goToNext}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 btn btn-circle btn-lg bg-black bg-opacity-50 text-white border-none hover:bg-opacity-70 z-10"
            title="Next (→)"
          >
            <ChevronRight className="h-6 w-6" />
          </button>
        </>
      )}

      {/* Main image */}
      <div className="flex items-center justify-center w-full h-full p-16">
        <img
          src={currentImage.url}
          alt={currentImage.original_filename}
          className="max-w-full max-h-full object-contain transition-transform duration-200"
          style={{ transform: `scale(${zoom})` }}
          onClick={() => setZoom(zoom === 1 ? 2 : 1)}
        />
      </div>

      {/* Thumbnail strip */}
      {images.length > 1 && (
        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 p-4">
          <div className="flex justify-center space-x-2 overflow-x-auto max-w-full">
            {images.map((image, index) => (
              <button
                key={image.id}
                onClick={() => {
                  setCurrentIndex(index);
                  setZoom(1);
                }}
                className={`
                  flex-shrink-0 w-16 h-16 rounded border-2 overflow-hidden transition-all
                  ${index === currentIndex 
                    ? 'border-white' 
                    : 'border-transparent hover:border-white/50'
                  }
                `}
              >
                <img
                  src={image.thumbnail_url || image.url}
                  alt={image.original_filename}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Click outside to close */}
      <div 
        className="absolute inset-0 -z-10"
        onClick={onClose}
      />
    </div>
  );
}
