/*! 🌼 daisyUI 5.0.42 - MIT License */ @layer utilities{.progress{appearance:none;border-radius:var(--radius-box);background-color:color-mix(in oklab,currentColor 20%,transparent);width:100%;height:.5rem;color:var(--color-base-content);position:relative;overflow:hidden;&:indeterminate{background-image:repeating-linear-gradient(90deg,currentColor -1% 10%,#0000 10% 90%);background-position-x:15%;background-size:200%;animation:5s ease-in-out infinite progress;@supports ((-moz-appearance:none)){&::-moz-progress-bar{background-color:#0000;background-image:repeating-linear-gradient(90deg,currentColor -1% 10%,#0000 10% 90%);background-position-x:15%;background-size:200%;animation:5s ease-in-out infinite progress}}}@supports ((-moz-appearance:none)){&::-moz-progress-bar{border-radius:var(--radius-box);background-color:currentColor}}@supports ((-webkit-appearance:none)){&::-webkit-progress-bar{border-radius:var(--radius-box);background-color:#0000}&::-webkit-progress-value{border-radius:var(--radius-box);background-color:currentColor}}}.progress-primary{color:var(--color-primary)}.progress-secondary{color:var(--color-secondary)}.progress-accent{color:var(--color-accent)}.progress-neutral{color:var(--color-neutral)}.progress-info{color:var(--color-info)}.progress-success{color:var(--color-success)}.progress-warning{color:var(--color-warning)}.progress-error{color:var(--color-error)}@keyframes progress{50%{background-position-x:-115%}}@media (width>=640px){.sm\:progress{appearance:none;border-radius:var(--radius-box);background-color:color-mix(in oklab,currentColor 20%,transparent);width:100%;height:.5rem;color:var(--color-base-content);position:relative;overflow:hidden;&:indeterminate{background-image:repeating-linear-gradient(90deg,currentColor -1% 10%,#0000 10% 90%);background-position-x:15%;background-size:200%;animation:5s ease-in-out infinite progress;@supports ((-moz-appearance:none)){&::-moz-progress-bar{background-color:#0000;background-image:repeating-linear-gradient(90deg,currentColor -1% 10%,#0000 10% 90%);background-position-x:15%;background-size:200%;animation:5s ease-in-out infinite progress}}}@supports ((-moz-appearance:none)){&::-moz-progress-bar{border-radius:var(--radius-box);background-color:currentColor}}@supports ((-webkit-appearance:none)){&::-webkit-progress-bar{border-radius:var(--radius-box);background-color:#0000}&::-webkit-progress-value{border-radius:var(--radius-box);background-color:currentColor}}}.sm\:progress-primary{color:var(--color-primary)}.sm\:progress-secondary{color:var(--color-secondary)}.sm\:progress-accent{color:var(--color-accent)}.sm\:progress-neutral{color:var(--color-neutral)}.sm\:progress-info{color:var(--color-info)}.sm\:progress-success{color:var(--color-success)}.sm\:progress-warning{color:var(--color-warning)}.sm\:progress-error{color:var(--color-error)}}@media (width>=768px){.md\:progress{appearance:none;border-radius:var(--radius-box);background-color:color-mix(in oklab,currentColor 20%,transparent);width:100%;height:.5rem;color:var(--color-base-content);position:relative;overflow:hidden;&:indeterminate{background-image:repeating-linear-gradient(90deg,currentColor -1% 10%,#0000 10% 90%);background-position-x:15%;background-size:200%;animation:5s ease-in-out infinite progress;@supports ((-moz-appearance:none)){&::-moz-progress-bar{background-color:#0000;background-image:repeating-linear-gradient(90deg,currentColor -1% 10%,#0000 10% 90%);background-position-x:15%;background-size:200%;animation:5s ease-in-out infinite progress}}}@supports ((-moz-appearance:none)){&::-moz-progress-bar{border-radius:var(--radius-box);background-color:currentColor}}@supports ((-webkit-appearance:none)){&::-webkit-progress-bar{border-radius:var(--radius-box);background-color:#0000}&::-webkit-progress-value{border-radius:var(--radius-box);background-color:currentColor}}}.md\:progress-primary{color:var(--color-primary)}.md\:progress-secondary{color:var(--color-secondary)}.md\:progress-accent{color:var(--color-accent)}.md\:progress-neutral{color:var(--color-neutral)}.md\:progress-info{color:var(--color-info)}.md\:progress-success{color:var(--color-success)}.md\:progress-warning{color:var(--color-warning)}.md\:progress-error{color:var(--color-error)}}@media (width>=1024px){.lg\:progress{appearance:none;border-radius:var(--radius-box);background-color:color-mix(in oklab,currentColor 20%,transparent);width:100%;height:.5rem;color:var(--color-base-content);position:relative;overflow:hidden;&:indeterminate{background-image:repeating-linear-gradient(90deg,currentColor -1% 10%,#0000 10% 90%);background-position-x:15%;background-size:200%;animation:5s ease-in-out infinite progress;@supports ((-moz-appearance:none)){&::-moz-progress-bar{background-color:#0000;background-image:repeating-linear-gradient(90deg,currentColor -1% 10%,#0000 10% 90%);background-position-x:15%;background-size:200%;animation:5s ease-in-out infinite progress}}}@supports ((-moz-appearance:none)){&::-moz-progress-bar{border-radius:var(--radius-box);background-color:currentColor}}@supports ((-webkit-appearance:none)){&::-webkit-progress-bar{border-radius:var(--radius-box);background-color:#0000}&::-webkit-progress-value{border-radius:var(--radius-box);background-color:currentColor}}}.lg\:progress-primary{color:var(--color-primary)}.lg\:progress-secondary{color:var(--color-secondary)}.lg\:progress-accent{color:var(--color-accent)}.lg\:progress-neutral{color:var(--color-neutral)}.lg\:progress-info{color:var(--color-info)}.lg\:progress-success{color:var(--color-success)}.lg\:progress-warning{color:var(--color-warning)}.lg\:progress-error{color:var(--color-error)}}@media (width>=1280px){.xl\:progress{appearance:none;border-radius:var(--radius-box);background-color:color-mix(in oklab,currentColor 20%,transparent);width:100%;height:.5rem;color:var(--color-base-content);position:relative;overflow:hidden;&:indeterminate{background-image:repeating-linear-gradient(90deg,currentColor -1% 10%,#0000 10% 90%);background-position-x:15%;background-size:200%;animation:5s ease-in-out infinite progress;@supports ((-moz-appearance:none)){&::-moz-progress-bar{background-color:#0000;background-image:repeating-linear-gradient(90deg,currentColor -1% 10%,#0000 10% 90%);background-position-x:15%;background-size:200%;animation:5s ease-in-out infinite progress}}}@supports ((-moz-appearance:none)){&::-moz-progress-bar{border-radius:var(--radius-box);background-color:currentColor}}@supports ((-webkit-appearance:none)){&::-webkit-progress-bar{border-radius:var(--radius-box);background-color:#0000}&::-webkit-progress-value{border-radius:var(--radius-box);background-color:currentColor}}}.xl\:progress-primary{color:var(--color-primary)}.xl\:progress-secondary{color:var(--color-secondary)}.xl\:progress-accent{color:var(--color-accent)}.xl\:progress-neutral{color:var(--color-neutral)}.xl\:progress-info{color:var(--color-info)}.xl\:progress-success{color:var(--color-success)}.xl\:progress-warning{color:var(--color-warning)}.xl\:progress-error{color:var(--color-error)}}@media (width>=1536px){.\32 xl\:progress{appearance:none;border-radius:var(--radius-box);background-color:color-mix(in oklab,currentColor 20%,transparent);width:100%;height:.5rem;color:var(--color-base-content);position:relative;overflow:hidden;&:indeterminate{background-image:repeating-linear-gradient(90deg,currentColor -1% 10%,#0000 10% 90%);background-position-x:15%;background-size:200%;animation:5s ease-in-out infinite progress;@supports ((-moz-appearance:none)){&::-moz-progress-bar{background-color:#0000;background-image:repeating-linear-gradient(90deg,currentColor -1% 10%,#0000 10% 90%);background-position-x:15%;background-size:200%;animation:5s ease-in-out infinite progress}}}@supports ((-moz-appearance:none)){&::-moz-progress-bar{border-radius:var(--radius-box);background-color:currentColor}}@supports ((-webkit-appearance:none)){&::-webkit-progress-bar{border-radius:var(--radius-box);background-color:#0000}&::-webkit-progress-value{border-radius:var(--radius-box);background-color:currentColor}}}.\32 xl\:progress-primary{color:var(--color-primary)}.\32 xl\:progress-secondary{color:var(--color-secondary)}.\32 xl\:progress-accent{color:var(--color-accent)}.\32 xl\:progress-neutral{color:var(--color-neutral)}.\32 xl\:progress-info{color:var(--color-info)}.\32 xl\:progress-success{color:var(--color-success)}.\32 xl\:progress-warning{color:var(--color-warning)}.\32 xl\:progress-error{color:var(--color-error)}}}