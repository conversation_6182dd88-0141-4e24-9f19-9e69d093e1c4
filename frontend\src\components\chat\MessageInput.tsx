'use client';

import { useState, useRef, useCallback } from 'react';
import { Send, Paperclip, Smile, X } from 'lucide-react';
import { useChat } from '@/contexts/ChatContext';
import { debounce } from '@/lib/utils';
import EmojiPicker from './EmojiPicker';
import socketManager from '@/lib/socket';

export default function MessageInput() {
  const { currentRoom, sendMessage } = useChat();
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Debounced typing indicator
  const debouncedStopTyping = useCallback(
    debounce(() => {
      if (currentRoom && isTyping) {
        socketManager.stopTyping(currentRoom.id);
        setIsTyping(false);
      }
    }, 1000),
    [currentRoom, isTyping]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setMessage(value);

    // Handle typing indicator
    if (currentRoom && value.trim() && !isTyping) {
      socketManager.startTyping(currentRoom.id);
      setIsTyping(true);
    }

    if (value.trim()) {
      debouncedStopTyping();
    } else if (isTyping) {
      socketManager.stopTyping(currentRoom.id);
      setIsTyping(false);
    }

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleSend = async () => {
    if (!message.trim() && attachments.length === 0) return;

    const messageText = message.trim();
    const files = [...attachments];

    // Clear input immediately
    setMessage('');
    setAttachments([]);
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }

    // Stop typing indicator
    if (currentRoom && isTyping) {
      socketManager.stopTyping(currentRoom.id);
      setIsTyping(false);
    }

    // Send message
    await sendMessage(messageText, files);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setAttachments(prev => [...prev, ...files]);
    
    // Clear the input so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleEmojiSelect = (emoji: string) => {
    setMessage(prev => prev + emoji);
    setShowEmojiPicker(false);
    textareaRef.current?.focus();
  };

  if (!currentRoom) {
    return null;
  }

  return (
    <div className="space-y-3">
      {/* Attachments preview */}
      {attachments.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {attachments.map((file, index) => (
            <div key={index} className="relative">
              <div className="bg-base-200 rounded-lg p-2 pr-8 max-w-xs">
                <p className="text-sm font-medium truncate">{file.name}</p>
                <p className="text-xs text-base-content/70">
                  {(file.size / 1024).toFixed(1)} KB
                </p>
              </div>
              <button
                onClick={() => removeAttachment(index)}
                className="absolute -top-1 -right-1 btn btn-circle btn-xs btn-error"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Message input */}
      <div className="flex items-end space-x-2">
        {/* File attachment button */}
        <button
          onClick={() => fileInputRef.current?.click()}
          className="btn btn-ghost btn-sm"
          title="Attach file"
        >
          <Paperclip className="h-4 w-4" />
        </button>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          onChange={handleFileSelect}
          accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar"
        />

        {/* Message textarea */}
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder="Type a message..."
            className="textarea textarea-bordered w-full resize-none min-h-[44px] max-h-32 pr-12"
            rows={1}
          />
          
          {/* Emoji button */}
          <button
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 btn btn-ghost btn-sm"
            title="Add emoji"
          >
            <Smile className="h-4 w-4" />
          </button>

          {/* Emoji picker */}
          {showEmojiPicker && (
            <div className="absolute bottom-full right-0 mb-2 z-10">
              <EmojiPicker
                onEmojiSelect={handleEmojiSelect}
                onClose={() => setShowEmojiPicker(false)}
              />
            </div>
          )}
        </div>

        {/* Send button */}
        <button
          onClick={handleSend}
          disabled={!message.trim() && attachments.length === 0}
          className="btn btn-primary btn-sm"
          title="Send message"
        >
          <Send className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}
