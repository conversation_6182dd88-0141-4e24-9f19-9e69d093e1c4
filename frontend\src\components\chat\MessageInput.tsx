'use client';

import { useState, useRef, useCallback } from 'react';
import { Send, Paperclip, Smile, X } from 'lucide-react';
import { useChat } from '@/contexts/ChatContext';
import { debounce } from '@/lib/utils';
import EmojiPicker from './EmojiPicker';
import FileUploadArea from './FileUploadArea';
import socketManager from '@/lib/socket';

export default function MessageInput() {
  const { currentRoom, sendMessage } = useChat();
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Debounced typing indicator
  const debouncedStopTyping = useCallback(
    debounce(() => {
      if (currentRoom && isTyping) {
        socketManager.stopTyping(currentRoom.id);
        setIsTyping(false);
      }
    }, 1000),
    [currentRoom, isTyping]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setMessage(value);

    // Handle typing indicator
    if (currentRoom && value.trim() && !isTyping) {
      socketManager.startTyping(currentRoom.id);
      setIsTyping(true);
    }

    if (value.trim()) {
      debouncedStopTyping();
    } else if (isTyping) {
      socketManager.stopTyping(currentRoom.id);
      setIsTyping(false);
    }

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleSend = async () => {
    if (!message.trim() && attachments.length === 0) return;

    const messageText = message.trim();
    const files = [...attachments];

    // Clear input immediately
    setMessage('');
    setAttachments([]);
    setShowFileUpload(false);
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }

    // Stop typing indicator
    if (currentRoom && isTyping) {
      socketManager.stopTyping(currentRoom.id);
      setIsTyping(false);
    }

    // Send message
    await sendMessage(messageText, files);
  };

  const handleFilesSelected = (files: File[]) => {
    setAttachments(files);
    if (files.length > 0) {
      setShowFileUpload(true);
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    setMessage(prev => prev + emoji);
    setShowEmojiPicker(false);
    textareaRef.current?.focus();
  };

  if (!currentRoom) {
    return null;
  }

  return (
    <div className="space-y-3">
      {/* File Upload Area */}
      {showFileUpload && (
        <div className="border-t border-base-300 pt-3">
          <FileUploadArea
            onFilesSelected={handleFilesSelected}
            maxFiles={5}
            maxFileSize={50}
            className="mb-3"
          />
        </div>
      )}

      {/* Message input */}
      <div className="flex items-end space-x-2">
        {/* File attachment button */}
        <button
          onClick={() => setShowFileUpload(!showFileUpload)}
          className={`btn btn-ghost btn-sm ${showFileUpload ? 'btn-active' : ''}`}
          title="Attach file"
        >
          <Paperclip className="h-4 w-4" />
        </button>

        {/* Message textarea */}
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder="Type a message..."
            className="textarea textarea-bordered w-full resize-none min-h-[44px] max-h-32 pr-12"
            rows={1}
          />
          
          {/* Emoji button */}
          <button
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 btn btn-ghost btn-sm"
            title="Add emoji"
          >
            <Smile className="h-4 w-4" />
          </button>

          {/* Emoji picker */}
          {showEmojiPicker && (
            <div className="absolute bottom-full right-0 mb-2 z-10">
              <EmojiPicker
                onEmojiSelect={handleEmojiSelect}
                onClose={() => setShowEmojiPicker(false)}
              />
            </div>
          )}
        </div>

        {/* Send button */}
        <button
          onClick={handleSend}
          disabled={!message.trim() && attachments.length === 0}
          className="btn btn-primary btn-sm"
          title="Send message"
        >
          <Send className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}
