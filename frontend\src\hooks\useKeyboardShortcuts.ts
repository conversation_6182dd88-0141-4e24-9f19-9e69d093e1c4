import { useEffect } from 'react';

interface KeyboardShortcuts {
  onSearch?: () => void;
  onNewMessage?: () => void;
  onToggleSidebar?: () => void;
  onToggleUserList?: () => void;
  onEscape?: () => void;
}

export function useKeyboardShortcuts({
  onSearch,
  onNewMessage,
  onToggleSidebar,
  onToggleUserList,
  onEscape,
}: KeyboardShortcuts) {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs
      const target = event.target as HTMLElement;
      if (
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.contentEditable === 'true'
      ) {
        // Only allow Escape in inputs
        if (event.key === 'Escape' && onEscape) {
          onEscape();
        }
        return;
      }

      // Handle keyboard shortcuts
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'k':
          case 'f':
            event.preventDefault();
            onSearch?.();
            break;
          case 'n':
            event.preventDefault();
            onNewMessage?.();
            break;
          case 'b':
            event.preventDefault();
            onToggleSidebar?.();
            break;
          case 'u':
            event.preventDefault();
            onToggleUserList?.();
            break;
        }
      } else {
        switch (event.key) {
          case 'Escape':
            onEscape?.();
            break;
          case '/':
            event.preventDefault();
            onSearch?.();
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onSearch, onNewMessage, onToggleSidebar, onToggleUserList, onEscape]);
}
