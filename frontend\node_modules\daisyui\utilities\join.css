/*! 🌼 daisyUI 5.0.42 - MIT License */ @layer utilities{.join{--join-ss:0;--join-se:0;--join-es:0;--join-ee:0;align-items:stretch;display:inline-flex;& :where(.join-item){border-start-start-radius:var(--join-ss,0);border-start-end-radius:var(--join-se,0);border-end-end-radius:var(--join-ee,0);border-end-start-radius:var(--join-es,0);& *{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}&>.join-item:where(:first-child){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}& :first-child:not(:last-child){& :where(.join-item){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}}&>.join-item:where(:last-child){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}& :last-child:not(:first-child){& :where(.join-item){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}}&>.join-item:where(:only-child){--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& :where(.join-item){--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}}.join-item{&:where(:not(:first-child,:disabled,[disabled],.btn-disabled)){margin-block-start:0;margin-inline-start:calc(var(--border,1px)*-1)}&:where(:is(:disabled,[disabled],.btn-disabled)){border-width:var(--border,1px)0 var(--border,1px)var(--border,1px)}}.join-vertical{flex-direction:column;&>.join-item:first-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:0;--join-ee:0}& :first-child:not(:last-child){& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:0;--join-ee:0}}&>.join-item:last-child{--join-ss:0;--join-se:0;--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :last-child:not(:first-child){& .join-item{--join-ss:0;--join-se:0;--join-es:var(--radius-field);--join-ee:var(--radius-field)}}&>.join-item:only-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}& .join-item{&:where(:not(:first-child)){margin-block-start:calc(var(--border,1px)*-1);margin-inline-start:0}}}.join-horizontal{flex-direction:row;&>.join-item:first-child{--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}& :first-child:not(:last-child){& .join-item{--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}}&>.join-item:last-child{--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}& :last-child:not(:first-child){& .join-item{--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}}&>.join-item:only-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}& .join-item{&:where(:not(:first-child)){margin-block-start:0;margin-inline-start:calc(var(--border,1px)*-1)}}}@media (width>=640px){.sm\:join{--join-ss:0;--join-se:0;--join-es:0;--join-ee:0;align-items:stretch;display:inline-flex;& :where(.join-item){border-start-start-radius:var(--join-ss,0);border-start-end-radius:var(--join-se,0);border-end-end-radius:var(--join-ee,0);border-end-start-radius:var(--join-es,0);& *{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}&>.join-item:where(:first-child){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}& :first-child:not(:last-child){& :where(.join-item){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}}&>.join-item:where(:last-child){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}& :last-child:not(:first-child){& :where(.join-item){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}}&>.join-item:where(:only-child){--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& :where(.join-item){--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}}.sm\:join-item{&:where(:not(:first-child,:disabled,[disabled],.btn-disabled)){margin-block-start:0;margin-inline-start:calc(var(--border,1px)*-1)}&:where(:is(:disabled,[disabled],.btn-disabled)){border-width:var(--border,1px)0 var(--border,1px)var(--border,1px)}}.sm\:join-vertical{flex-direction:column;&>.join-item:first-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:0;--join-ee:0}& :first-child:not(:last-child){& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:0;--join-ee:0}}&>.join-item:last-child{--join-ss:0;--join-se:0;--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :last-child:not(:first-child){& .join-item{--join-ss:0;--join-se:0;--join-es:var(--radius-field);--join-ee:var(--radius-field)}}&>.join-item:only-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}& .join-item{&:where(:not(:first-child)){margin-block-start:calc(var(--border,1px)*-1);margin-inline-start:0}}}.sm\:join-horizontal{flex-direction:row;&>.join-item:first-child{--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}& :first-child:not(:last-child){& .join-item{--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}}&>.join-item:last-child{--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}& :last-child:not(:first-child){& .join-item{--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}}&>.join-item:only-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}& .join-item{&:where(:not(:first-child)){margin-block-start:0;margin-inline-start:calc(var(--border,1px)*-1)}}}}@media (width>=768px){.md\:join{--join-ss:0;--join-se:0;--join-es:0;--join-ee:0;align-items:stretch;display:inline-flex;& :where(.join-item){border-start-start-radius:var(--join-ss,0);border-start-end-radius:var(--join-se,0);border-end-end-radius:var(--join-ee,0);border-end-start-radius:var(--join-es,0);& *{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}&>.join-item:where(:first-child){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}& :first-child:not(:last-child){& :where(.join-item){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}}&>.join-item:where(:last-child){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}& :last-child:not(:first-child){& :where(.join-item){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}}&>.join-item:where(:only-child){--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& :where(.join-item){--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}}.md\:join-item{&:where(:not(:first-child,:disabled,[disabled],.btn-disabled)){margin-block-start:0;margin-inline-start:calc(var(--border,1px)*-1)}&:where(:is(:disabled,[disabled],.btn-disabled)){border-width:var(--border,1px)0 var(--border,1px)var(--border,1px)}}.md\:join-vertical{flex-direction:column;&>.join-item:first-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:0;--join-ee:0}& :first-child:not(:last-child){& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:0;--join-ee:0}}&>.join-item:last-child{--join-ss:0;--join-se:0;--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :last-child:not(:first-child){& .join-item{--join-ss:0;--join-se:0;--join-es:var(--radius-field);--join-ee:var(--radius-field)}}&>.join-item:only-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}& .join-item{&:where(:not(:first-child)){margin-block-start:calc(var(--border,1px)*-1);margin-inline-start:0}}}.md\:join-horizontal{flex-direction:row;&>.join-item:first-child{--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}& :first-child:not(:last-child){& .join-item{--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}}&>.join-item:last-child{--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}& :last-child:not(:first-child){& .join-item{--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}}&>.join-item:only-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}& .join-item{&:where(:not(:first-child)){margin-block-start:0;margin-inline-start:calc(var(--border,1px)*-1)}}}}@media (width>=1024px){.lg\:join{--join-ss:0;--join-se:0;--join-es:0;--join-ee:0;align-items:stretch;display:inline-flex;& :where(.join-item){border-start-start-radius:var(--join-ss,0);border-start-end-radius:var(--join-se,0);border-end-end-radius:var(--join-ee,0);border-end-start-radius:var(--join-es,0);& *{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}&>.join-item:where(:first-child){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}& :first-child:not(:last-child){& :where(.join-item){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}}&>.join-item:where(:last-child){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}& :last-child:not(:first-child){& :where(.join-item){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}}&>.join-item:where(:only-child){--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& :where(.join-item){--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}}.lg\:join-item{&:where(:not(:first-child,:disabled,[disabled],.btn-disabled)){margin-block-start:0;margin-inline-start:calc(var(--border,1px)*-1)}&:where(:is(:disabled,[disabled],.btn-disabled)){border-width:var(--border,1px)0 var(--border,1px)var(--border,1px)}}.lg\:join-vertical{flex-direction:column;&>.join-item:first-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:0;--join-ee:0}& :first-child:not(:last-child){& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:0;--join-ee:0}}&>.join-item:last-child{--join-ss:0;--join-se:0;--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :last-child:not(:first-child){& .join-item{--join-ss:0;--join-se:0;--join-es:var(--radius-field);--join-ee:var(--radius-field)}}&>.join-item:only-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}& .join-item{&:where(:not(:first-child)){margin-block-start:calc(var(--border,1px)*-1);margin-inline-start:0}}}.lg\:join-horizontal{flex-direction:row;&>.join-item:first-child{--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}& :first-child:not(:last-child){& .join-item{--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}}&>.join-item:last-child{--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}& :last-child:not(:first-child){& .join-item{--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}}&>.join-item:only-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}& .join-item{&:where(:not(:first-child)){margin-block-start:0;margin-inline-start:calc(var(--border,1px)*-1)}}}}@media (width>=1280px){.xl\:join{--join-ss:0;--join-se:0;--join-es:0;--join-ee:0;align-items:stretch;display:inline-flex;& :where(.join-item){border-start-start-radius:var(--join-ss,0);border-start-end-radius:var(--join-se,0);border-end-end-radius:var(--join-ee,0);border-end-start-radius:var(--join-es,0);& *{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}&>.join-item:where(:first-child){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}& :first-child:not(:last-child){& :where(.join-item){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}}&>.join-item:where(:last-child){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}& :last-child:not(:first-child){& :where(.join-item){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}}&>.join-item:where(:only-child){--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& :where(.join-item){--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}}.xl\:join-item{&:where(:not(:first-child,:disabled,[disabled],.btn-disabled)){margin-block-start:0;margin-inline-start:calc(var(--border,1px)*-1)}&:where(:is(:disabled,[disabled],.btn-disabled)){border-width:var(--border,1px)0 var(--border,1px)var(--border,1px)}}.xl\:join-vertical{flex-direction:column;&>.join-item:first-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:0;--join-ee:0}& :first-child:not(:last-child){& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:0;--join-ee:0}}&>.join-item:last-child{--join-ss:0;--join-se:0;--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :last-child:not(:first-child){& .join-item{--join-ss:0;--join-se:0;--join-es:var(--radius-field);--join-ee:var(--radius-field)}}&>.join-item:only-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}& .join-item{&:where(:not(:first-child)){margin-block-start:calc(var(--border,1px)*-1);margin-inline-start:0}}}.xl\:join-horizontal{flex-direction:row;&>.join-item:first-child{--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}& :first-child:not(:last-child){& .join-item{--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}}&>.join-item:last-child{--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}& :last-child:not(:first-child){& .join-item{--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}}&>.join-item:only-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}& .join-item{&:where(:not(:first-child)){margin-block-start:0;margin-inline-start:calc(var(--border,1px)*-1)}}}}@media (width>=1536px){.\32 xl\:join{--join-ss:0;--join-se:0;--join-es:0;--join-ee:0;align-items:stretch;display:inline-flex;& :where(.join-item){border-start-start-radius:var(--join-ss,0);border-start-end-radius:var(--join-se,0);border-end-end-radius:var(--join-ee,0);border-end-start-radius:var(--join-es,0);& *{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}&>.join-item:where(:first-child){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}& :first-child:not(:last-child){& :where(.join-item){--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}}&>.join-item:where(:last-child){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}& :last-child:not(:first-child){& :where(.join-item){--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}}&>.join-item:where(:only-child){--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& :where(.join-item){--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}}.\32 xl\:join-item{&:where(:not(:first-child,:disabled,[disabled],.btn-disabled)){margin-block-start:0;margin-inline-start:calc(var(--border,1px)*-1)}&:where(:is(:disabled,[disabled],.btn-disabled)){border-width:var(--border,1px)0 var(--border,1px)var(--border,1px)}}.\32 xl\:join-vertical{flex-direction:column;&>.join-item:first-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:0;--join-ee:0}& :first-child:not(:last-child){& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:0;--join-ee:0}}&>.join-item:last-child{--join-ss:0;--join-se:0;--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :last-child:not(:first-child){& .join-item{--join-ss:0;--join-se:0;--join-es:var(--radius-field);--join-ee:var(--radius-field)}}&>.join-item:only-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}& .join-item{&:where(:not(:first-child)){margin-block-start:calc(var(--border,1px)*-1);margin-inline-start:0}}}.\32 xl\:join-horizontal{flex-direction:row;&>.join-item:first-child{--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}& :first-child:not(:last-child){& .join-item{--join-ss:var(--radius-field);--join-se:0;--join-es:var(--radius-field);--join-ee:0}}&>.join-item:last-child{--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}& :last-child:not(:first-child){& .join-item{--join-ss:0;--join-se:var(--radius-field);--join-es:0;--join-ee:var(--radius-field)}}&>.join-item:only-child{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}& :only-child{& .join-item{--join-ss:var(--radius-field);--join-se:var(--radius-field);--join-es:var(--radius-field);--join-ee:var(--radius-field)}}& .join-item{&:where(:not(:first-child)){margin-block-start:0;margin-inline-start:calc(var(--border,1px)*-1)}}}}}