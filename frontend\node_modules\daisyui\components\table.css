/*! 🌼 daisyUI 5.0.42 - MIT License */ @layer utilities{.table{border-radius:var(--radius-box);text-align:left;width:100%;font-size:.875rem;position:relative;&:where(:dir(rtl),[dir=rtl],[dir=rtl] *){text-align:right}& tr.row-hover{&,&:nth-child(2n){&:hover{@media (hover:hover){&{background-color:var(--color-base-200)}}}}}& :where(th,td){vertical-align:middle;padding-block:.75rem;padding-inline:1rem}& :where(thead,tfoot){white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);font-size:.875rem;font-weight:600}& :where(tfoot){border-top:var(--border)solid color-mix(in oklch,var(--color-base-content)5%,#0000)}& :where(.table-pin-rows thead tr){z-index:1;background-color:var(--color-base-100);position:sticky;top:0}& :where(.table-pin-rows tfoot tr){z-index:1;background-color:var(--color-base-100);position:sticky;bottom:0}& :where(.table-pin-cols tr th){background-color:var(--color-base-100);position:sticky;left:0;right:0}& :where(thead tr,tbody tr:not(:last-child)){border-bottom:var(--border)solid color-mix(in oklch,var(--color-base-content)5%,#0000)}}.table-zebra{& tbody{& tr{&:where(:nth-child(2n)){background-color:var(--color-base-200);& :where(.table-pin-cols tr th){background-color:var(--color-base-200)}}&.row-hover{&,&:where(:nth-child(2n)){&:hover{@media (hover:hover){&{background-color:var(--color-base-300)}}}}}}}}.table-xs{& :not(thead,tfoot) tr{font-size:.6875rem}& :where(th,td){padding-block:.25rem;padding-inline:.5rem}}.table-sm{& :not(thead,tfoot) tr{font-size:.75rem}& :where(th,td){padding-block:.5rem;padding-inline:.75rem}}.table-md{& :not(thead,tfoot) tr{font-size:.875rem}& :where(th,td){padding-block:.75rem;padding-inline:1rem}}.table-lg{& :not(thead,tfoot) tr{font-size:1.125rem}& :where(th,td){padding-block:1rem;padding-inline:1.25rem}}.table-xl{& :not(thead,tfoot) tr{font-size:1.375rem}& :where(th,td){padding-block:1.25rem;padding-inline:1.5rem}}@media (width>=640px){.sm\:table{border-radius:var(--radius-box);text-align:left;width:100%;font-size:.875rem;position:relative;&:where(:dir(rtl),[dir=rtl],[dir=rtl] *){text-align:right}& tr.row-hover{&,&:nth-child(2n){&:hover{@media (hover:hover){&{background-color:var(--color-base-200)}}}}}& :where(th,td){vertical-align:middle;padding-block:.75rem;padding-inline:1rem}& :where(thead,tfoot){white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);font-size:.875rem;font-weight:600}& :where(tfoot){border-top:var(--border)solid color-mix(in oklch,var(--color-base-content)5%,#0000)}& :where(.table-pin-rows thead tr){z-index:1;background-color:var(--color-base-100);position:sticky;top:0}& :where(.table-pin-rows tfoot tr){z-index:1;background-color:var(--color-base-100);position:sticky;bottom:0}& :where(.table-pin-cols tr th){background-color:var(--color-base-100);position:sticky;left:0;right:0}& :where(thead tr,tbody tr:not(:last-child)){border-bottom:var(--border)solid color-mix(in oklch,var(--color-base-content)5%,#0000)}}.sm\:table-zebra{& tbody{& tr{&:where(:nth-child(2n)){background-color:var(--color-base-200);& :where(.table-pin-cols tr th){background-color:var(--color-base-200)}}&.row-hover{&,&:where(:nth-child(2n)){&:hover{@media (hover:hover){&{background-color:var(--color-base-300)}}}}}}}}.sm\:table-xs{& :not(thead,tfoot) tr{font-size:.6875rem}& :where(th,td){padding-block:.25rem;padding-inline:.5rem}}.sm\:table-sm{& :not(thead,tfoot) tr{font-size:.75rem}& :where(th,td){padding-block:.5rem;padding-inline:.75rem}}.sm\:table-md{& :not(thead,tfoot) tr{font-size:.875rem}& :where(th,td){padding-block:.75rem;padding-inline:1rem}}.sm\:table-lg{& :not(thead,tfoot) tr{font-size:1.125rem}& :where(th,td){padding-block:1rem;padding-inline:1.25rem}}.sm\:table-xl{& :not(thead,tfoot) tr{font-size:1.375rem}& :where(th,td){padding-block:1.25rem;padding-inline:1.5rem}}}@media (width>=768px){.md\:table{border-radius:var(--radius-box);text-align:left;width:100%;font-size:.875rem;position:relative;&:where(:dir(rtl),[dir=rtl],[dir=rtl] *){text-align:right}& tr.row-hover{&,&:nth-child(2n){&:hover{@media (hover:hover){&{background-color:var(--color-base-200)}}}}}& :where(th,td){vertical-align:middle;padding-block:.75rem;padding-inline:1rem}& :where(thead,tfoot){white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);font-size:.875rem;font-weight:600}& :where(tfoot){border-top:var(--border)solid color-mix(in oklch,var(--color-base-content)5%,#0000)}& :where(.table-pin-rows thead tr){z-index:1;background-color:var(--color-base-100);position:sticky;top:0}& :where(.table-pin-rows tfoot tr){z-index:1;background-color:var(--color-base-100);position:sticky;bottom:0}& :where(.table-pin-cols tr th){background-color:var(--color-base-100);position:sticky;left:0;right:0}& :where(thead tr,tbody tr:not(:last-child)){border-bottom:var(--border)solid color-mix(in oklch,var(--color-base-content)5%,#0000)}}.md\:table-zebra{& tbody{& tr{&:where(:nth-child(2n)){background-color:var(--color-base-200);& :where(.table-pin-cols tr th){background-color:var(--color-base-200)}}&.row-hover{&,&:where(:nth-child(2n)){&:hover{@media (hover:hover){&{background-color:var(--color-base-300)}}}}}}}}.md\:table-xs{& :not(thead,tfoot) tr{font-size:.6875rem}& :where(th,td){padding-block:.25rem;padding-inline:.5rem}}.md\:table-sm{& :not(thead,tfoot) tr{font-size:.75rem}& :where(th,td){padding-block:.5rem;padding-inline:.75rem}}.md\:table-md{& :not(thead,tfoot) tr{font-size:.875rem}& :where(th,td){padding-block:.75rem;padding-inline:1rem}}.md\:table-lg{& :not(thead,tfoot) tr{font-size:1.125rem}& :where(th,td){padding-block:1rem;padding-inline:1.25rem}}.md\:table-xl{& :not(thead,tfoot) tr{font-size:1.375rem}& :where(th,td){padding-block:1.25rem;padding-inline:1.5rem}}}@media (width>=1024px){.lg\:table{border-radius:var(--radius-box);text-align:left;width:100%;font-size:.875rem;position:relative;&:where(:dir(rtl),[dir=rtl],[dir=rtl] *){text-align:right}& tr.row-hover{&,&:nth-child(2n){&:hover{@media (hover:hover){&{background-color:var(--color-base-200)}}}}}& :where(th,td){vertical-align:middle;padding-block:.75rem;padding-inline:1rem}& :where(thead,tfoot){white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);font-size:.875rem;font-weight:600}& :where(tfoot){border-top:var(--border)solid color-mix(in oklch,var(--color-base-content)5%,#0000)}& :where(.table-pin-rows thead tr){z-index:1;background-color:var(--color-base-100);position:sticky;top:0}& :where(.table-pin-rows tfoot tr){z-index:1;background-color:var(--color-base-100);position:sticky;bottom:0}& :where(.table-pin-cols tr th){background-color:var(--color-base-100);position:sticky;left:0;right:0}& :where(thead tr,tbody tr:not(:last-child)){border-bottom:var(--border)solid color-mix(in oklch,var(--color-base-content)5%,#0000)}}.lg\:table-zebra{& tbody{& tr{&:where(:nth-child(2n)){background-color:var(--color-base-200);& :where(.table-pin-cols tr th){background-color:var(--color-base-200)}}&.row-hover{&,&:where(:nth-child(2n)){&:hover{@media (hover:hover){&{background-color:var(--color-base-300)}}}}}}}}.lg\:table-xs{& :not(thead,tfoot) tr{font-size:.6875rem}& :where(th,td){padding-block:.25rem;padding-inline:.5rem}}.lg\:table-sm{& :not(thead,tfoot) tr{font-size:.75rem}& :where(th,td){padding-block:.5rem;padding-inline:.75rem}}.lg\:table-md{& :not(thead,tfoot) tr{font-size:.875rem}& :where(th,td){padding-block:.75rem;padding-inline:1rem}}.lg\:table-lg{& :not(thead,tfoot) tr{font-size:1.125rem}& :where(th,td){padding-block:1rem;padding-inline:1.25rem}}.lg\:table-xl{& :not(thead,tfoot) tr{font-size:1.375rem}& :where(th,td){padding-block:1.25rem;padding-inline:1.5rem}}}@media (width>=1280px){.xl\:table{border-radius:var(--radius-box);text-align:left;width:100%;font-size:.875rem;position:relative;&:where(:dir(rtl),[dir=rtl],[dir=rtl] *){text-align:right}& tr.row-hover{&,&:nth-child(2n){&:hover{@media (hover:hover){&{background-color:var(--color-base-200)}}}}}& :where(th,td){vertical-align:middle;padding-block:.75rem;padding-inline:1rem}& :where(thead,tfoot){white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);font-size:.875rem;font-weight:600}& :where(tfoot){border-top:var(--border)solid color-mix(in oklch,var(--color-base-content)5%,#0000)}& :where(.table-pin-rows thead tr){z-index:1;background-color:var(--color-base-100);position:sticky;top:0}& :where(.table-pin-rows tfoot tr){z-index:1;background-color:var(--color-base-100);position:sticky;bottom:0}& :where(.table-pin-cols tr th){background-color:var(--color-base-100);position:sticky;left:0;right:0}& :where(thead tr,tbody tr:not(:last-child)){border-bottom:var(--border)solid color-mix(in oklch,var(--color-base-content)5%,#0000)}}.xl\:table-zebra{& tbody{& tr{&:where(:nth-child(2n)){background-color:var(--color-base-200);& :where(.table-pin-cols tr th){background-color:var(--color-base-200)}}&.row-hover{&,&:where(:nth-child(2n)){&:hover{@media (hover:hover){&{background-color:var(--color-base-300)}}}}}}}}.xl\:table-xs{& :not(thead,tfoot) tr{font-size:.6875rem}& :where(th,td){padding-block:.25rem;padding-inline:.5rem}}.xl\:table-sm{& :not(thead,tfoot) tr{font-size:.75rem}& :where(th,td){padding-block:.5rem;padding-inline:.75rem}}.xl\:table-md{& :not(thead,tfoot) tr{font-size:.875rem}& :where(th,td){padding-block:.75rem;padding-inline:1rem}}.xl\:table-lg{& :not(thead,tfoot) tr{font-size:1.125rem}& :where(th,td){padding-block:1rem;padding-inline:1.25rem}}.xl\:table-xl{& :not(thead,tfoot) tr{font-size:1.375rem}& :where(th,td){padding-block:1.25rem;padding-inline:1.5rem}}}@media (width>=1536px){.\32 xl\:table{border-radius:var(--radius-box);text-align:left;width:100%;font-size:.875rem;position:relative;&:where(:dir(rtl),[dir=rtl],[dir=rtl] *){text-align:right}& tr.row-hover{&,&:nth-child(2n){&:hover{@media (hover:hover){&{background-color:var(--color-base-200)}}}}}& :where(th,td){vertical-align:middle;padding-block:.75rem;padding-inline:1rem}& :where(thead,tfoot){white-space:nowrap;color:color-mix(in oklab,var(--color-base-content)60%,transparent);font-size:.875rem;font-weight:600}& :where(tfoot){border-top:var(--border)solid color-mix(in oklch,var(--color-base-content)5%,#0000)}& :where(.table-pin-rows thead tr){z-index:1;background-color:var(--color-base-100);position:sticky;top:0}& :where(.table-pin-rows tfoot tr){z-index:1;background-color:var(--color-base-100);position:sticky;bottom:0}& :where(.table-pin-cols tr th){background-color:var(--color-base-100);position:sticky;left:0;right:0}& :where(thead tr,tbody tr:not(:last-child)){border-bottom:var(--border)solid color-mix(in oklch,var(--color-base-content)5%,#0000)}}.\32 xl\:table-zebra{& tbody{& tr{&:where(:nth-child(2n)){background-color:var(--color-base-200);& :where(.table-pin-cols tr th){background-color:var(--color-base-200)}}&.row-hover{&,&:where(:nth-child(2n)){&:hover{@media (hover:hover){&{background-color:var(--color-base-300)}}}}}}}}.\32 xl\:table-xs{& :not(thead,tfoot) tr{font-size:.6875rem}& :where(th,td){padding-block:.25rem;padding-inline:.5rem}}.\32 xl\:table-sm{& :not(thead,tfoot) tr{font-size:.75rem}& :where(th,td){padding-block:.5rem;padding-inline:.75rem}}.\32 xl\:table-md{& :not(thead,tfoot) tr{font-size:.875rem}& :where(th,td){padding-block:.75rem;padding-inline:1rem}}.\32 xl\:table-lg{& :not(thead,tfoot) tr{font-size:1.125rem}& :where(th,td){padding-block:1rem;padding-inline:1.25rem}}.\32 xl\:table-xl{& :not(thead,tfoot) tr{font-size:1.375rem}& :where(th,td){padding-block:1.25rem;padding-inline:1.5rem}}}}