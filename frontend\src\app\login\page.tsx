'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Eye, EyeOff, MessageCircle, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { LoginForm } from '@/lib/types';

const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
});

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false);
  const { login, loading } = useAuth();
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginForm>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginForm) => {
    try {
      await login(data);
    } catch (error) {
      // Error is handled in the auth context
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 via-base-100 to-secondary/10 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Back to home */}
        <Link 
          href="/" 
          className="btn btn-ghost btn-sm mb-6 gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Home
        </Link>

        {/* Login Card */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            {/* Header */}
            <div className="text-center mb-6">
              <div className="flex items-center justify-center space-x-2 mb-4">
                <MessageCircle className="h-8 w-8 text-primary" />
                <span className="text-2xl font-bold">ChatApp</span>
              </div>
              <h1 className="text-3xl font-bold">Welcome Back</h1>
              <p className="text-base-content/70 mt-2">
                Sign in to continue chatting
              </p>
            </div>

            {/* Login Form */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Username Field */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium">Username</span>
                </label>
                <input
                  type="text"
                  placeholder="Enter your username"
                  className={`input input-bordered w-full ${
                    errors.username ? 'input-error' : ''
                  }`}
                  {...register('username')}
                />
                {errors.username && (
                  <label className="label">
                    <span className="label-text-alt text-error">
                      {errors.username.message}
                    </span>
                  </label>
                )}
              </div>

              {/* Password Field */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium">Password</span>
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter your password"
                    className={`input input-bordered w-full pr-12 ${
                      errors.password ? 'input-error' : ''
                    }`}
                    {...register('password')}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-base-content/50" />
                    ) : (
                      <Eye className="h-5 w-5 text-base-content/50" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <label className="label">
                    <span className="label-text-alt text-error">
                      {errors.password.message}
                    </span>
                  </label>
                )}
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                className={`btn btn-primary w-full ${
                  isSubmitting || loading ? 'loading' : ''
                }`}
                disabled={isSubmitting || loading}
              >
                {isSubmitting || loading ? 'Signing In...' : 'Sign In'}
              </button>
            </form>

            {/* Divider */}
            <div className="divider">or</div>

            {/* Register Link */}
            <div className="text-center">
              <p className="text-base-content/70">
                Don't have an account?{' '}
                <Link href="/register" className="link link-primary font-medium">
                  Create one here
                </Link>
              </p>
            </div>

            {/* Demo Credentials */}
            <div className="alert alert-info mt-4">
              <div className="text-sm">
                <p className="font-medium mb-1">Demo Credentials:</p>
                <p>Username: <code className="bg-base-200 px-1 rounded">demo</code></p>
                <p>Password: <code className="bg-base-200 px-1 rounded">demo123</code></p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-6 text-sm text-base-content/50">
          © 2024 ChatApp. All rights reserved.
        </div>
      </div>
    </div>
  );
}
