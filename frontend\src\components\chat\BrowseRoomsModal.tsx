'use client';

import { useState, useEffect } from 'react';
import { Search, X, Hash, Users, Plus } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useChat } from '@/contexts/ChatContext';
import { roomAPI } from '@/lib/api';
import { Room } from '@/lib/types';
import { formatDate } from '@/lib/utils';
import toast from 'react-hot-toast';

interface BrowseRoomsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function BrowseRoomsModal({ isOpen, onClose }: BrowseRoomsModalProps) {
  const { user } = useAuth();
  const { joinRoom: joinRoomContext, setCurrentRoom } = useChat();
  const [allRooms, setAllRooms] = useState<Room[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [joiningRooms, setJoiningRooms] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (isOpen) {
      loadAllRooms();
    }
  }, [isOpen]);

  const loadAllRooms = async () => {
    try {
      setLoading(true);
      const response = await roomAPI.getRooms();
      if (response.success) {
        // Show all public rooms and private rooms the user is already in
        const rooms = response.data.filter((room: Room) => 
          !room.is_private || room.participants.some(p => p.id === user?.id)
        );
        setAllRooms(rooms);
      }
    } catch (error) {
      console.error('Failed to load rooms:', error);
      toast.error('Failed to load rooms');
    } finally {
      setLoading(false);
    }
  };

  const filteredRooms = allRooms.filter(room =>
    room.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (room.description && room.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleJoinRoom = async (room: Room) => {
    if (joiningRooms.has(room.id)) return;

    try {
      setJoiningRooms(prev => new Set(prev).add(room.id));
      
      // Check if user is already in the room
      const isAlreadyMember = room.participants.some(p => p.id === user?.id);
      
      if (!isAlreadyMember) {
        await joinRoomContext(room.id);
        toast.success(`Joined ${room.name}`);
      } else {
        toast.success(`Switched to ${room.name}`);
      }
      
      // Switch to the room
      setCurrentRoom(room);
      onClose();
    } catch (error) {
      console.error('Failed to join room:', error);
      toast.error('Failed to join room');
    } finally {
      setJoiningRooms(prev => {
        const newSet = new Set(prev);
        newSet.delete(room.id);
        return newSet;
      });
    }
  };

  const isUserInRoom = (room: Room) => {
    return room.participants.some(p => p.id === user?.id);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-base-100 rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-base-300">
          <div className="flex items-center space-x-2">
            <Hash className="h-6 w-6 text-primary" />
            <h2 className="text-xl font-semibold">Browse Rooms</h2>
          </div>
          <button onClick={onClose} className="btn btn-ghost btn-sm">
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Search */}
        <div className="p-4 border-b border-base-300">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-base-content/50" />
            <input
              type="text"
              placeholder="Search rooms..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input input-bordered w-full pl-10"
            />
          </div>
        </div>

        {/* Rooms List */}
        <div className="flex-1 overflow-y-auto p-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="loading loading-spinner loading-md"></div>
            </div>
          ) : filteredRooms.length === 0 ? (
            <div className="text-center py-8 text-base-content/50">
              {searchTerm ? 'No rooms found matching your search' : 'No rooms available'}
            </div>
          ) : (
            <div className="space-y-3">
              {filteredRooms.map(room => {
                const isInRoom = isUserInRoom(room);
                const isJoining = joiningRooms.has(room.id);
                
                return (
                  <div
                    key={room.id}
                    className="border border-base-300 rounded-lg p-4 hover:bg-base-50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        {/* Room header */}
                        <div className="flex items-center space-x-2 mb-2">
                          <Hash className="h-4 w-4 text-base-content/50" />
                          <h3 className="font-semibold truncate">{room.name}</h3>
                          {room.is_private && (
                            <span className="badge badge-secondary badge-sm">Private</span>
                          )}
                          {isInRoom && (
                            <span className="badge badge-success badge-sm">Joined</span>
                          )}
                        </div>

                        {/* Room description */}
                        {room.description && (
                          <p className="text-sm text-base-content/70 mb-3 line-clamp-2">
                            {room.description}
                          </p>
                        )}

                        {/* Room stats */}
                        <div className="flex items-center space-x-4 text-xs text-base-content/50">
                          <div className="flex items-center space-x-1">
                            <Users className="h-3 w-3" />
                            <span>{room.participants.length} members</span>
                          </div>
                          <div>
                            Created {formatDate(room.created_at)}
                          </div>
                          {room.last_message && (
                            <div>
                              Last message {formatDate(room.last_message.created_at)}
                            </div>
                          )}
                        </div>

                        {/* Recent participants */}
                        {room.participants.length > 0 && (
                          <div className="mt-3">
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-base-content/50">Members:</span>
                              <div className="flex -space-x-1">
                                {room.participants.slice(0, 5).map(participant => (
                                  <div
                                    key={participant.id}
                                    className="avatar"
                                    title={participant.display_name}
                                  >
                                    <div className="w-6 h-6 rounded-full bg-primary text-primary-content flex items-center justify-center border-2 border-base-100">
                                      {participant.avatar_url ? (
                                        <img 
                                          src={participant.avatar_url} 
                                          alt={participant.display_name}
                                          className="w-full h-full rounded-full"
                                        />
                                      ) : (
                                        <span className="text-xs font-medium">
                                          {participant.display_name.charAt(0).toUpperCase()}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                ))}
                                {room.participants.length > 5 && (
                                  <div className="w-6 h-6 rounded-full bg-base-300 text-base-content flex items-center justify-center border-2 border-base-100 text-xs">
                                    +{room.participants.length - 5}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Join button */}
                      <div className="ml-4">
                        <button
                          onClick={() => handleJoinRoom(room)}
                          disabled={isJoining}
                          className={`btn btn-sm ${
                            isInRoom 
                              ? 'btn-outline' 
                              : 'btn-primary'
                          } ${isJoining ? 'loading' : ''}`}
                        >
                          {isJoining ? (
                            'Joining...'
                          ) : isInRoom ? (
                            'Switch to'
                          ) : (
                            <>
                              <Plus className="h-3 w-3 mr-1" />
                              Join
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-base-300">
          <button
            onClick={onClose}
            className="btn btn-ghost w-full"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
