/*! 🌼 daisyUI 5.0.42 - MIT License */ @layer utilities{.select{border:var(--border)solid #0000;appearance:none;background-color:var(--color-base-100);vertical-align:middle;width:clamp(3rem,20rem,100%);height:var(--size);touch-action:manipulation;text-overflow:ellipsis;box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;border-color:var(--input-color);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);--size:calc(var(--size-field,.25rem)*10);background-image:linear-gradient(45deg,#0000 50%,currentColor 50%),linear-gradient(135deg,currentColor 50%,#0000 50%);background-position:calc(100% - 20px) calc(1px + 50%),calc(100% - 16.1px) calc(1px + 50%);background-repeat:no-repeat;background-size:4px 4px,4px 4px;border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-shrink:1;align-items:center;gap:.375rem;padding-inline:1rem 1.75rem;font-size:.875rem;display:inline-flex;position:relative;[dir=rtl] &{background-position:12px calc(1px + 50%),16px calc(1px + 50%)}& select{appearance:none;background:inherit;border-radius:inherit;border-style:none;width:calc(100% + 2.75rem);height:calc(100% - 2px);margin-inline:-1rem -1.75rem;padding-inline:1rem 1.75rem;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:not(:last-child){background-image:none;margin-inline-end:-1.375rem}}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}&:has(>select[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>select[disabled])>select[disabled]{cursor:not-allowed}}.select-ghost{box-shadow:none;background-color:#0000;border-color:#0000;transition:background-color .2s;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.select-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.select-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.select-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.select-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.select-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.select-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.select-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.select-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.select-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem}.select-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem}.select-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem}.select-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem}.select-xl{--size:calc(var(--size-field,.25rem)*14);font-size:1.375rem}@media (width>=640px){.sm\:select{border:var(--border)solid #0000;appearance:none;background-color:var(--color-base-100);vertical-align:middle;width:clamp(3rem,20rem,100%);height:var(--size);touch-action:manipulation;text-overflow:ellipsis;box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;border-color:var(--input-color);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);--size:calc(var(--size-field,.25rem)*10);background-image:linear-gradient(45deg,#0000 50%,currentColor 50%),linear-gradient(135deg,currentColor 50%,#0000 50%);background-position:calc(100% - 20px) calc(1px + 50%),calc(100% - 16.1px) calc(1px + 50%);background-repeat:no-repeat;background-size:4px 4px,4px 4px;border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-shrink:1;align-items:center;gap:.375rem;padding-inline:1rem 1.75rem;font-size:.875rem;display:inline-flex;position:relative;[dir=rtl] &{background-position:12px calc(1px + 50%),16px calc(1px + 50%)}& select{appearance:none;background:inherit;border-radius:inherit;border-style:none;width:calc(100% + 2.75rem);height:calc(100% - 2px);margin-inline:-1rem -1.75rem;padding-inline:1rem 1.75rem;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:not(:last-child){background-image:none;margin-inline-end:-1.375rem}}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}&:has(>select[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>select[disabled])>select[disabled]{cursor:not-allowed}}.sm\:select-ghost{box-shadow:none;background-color:#0000;border-color:#0000;transition:background-color .2s;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.sm\:select-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.sm\:select-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.sm\:select-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.sm\:select-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.sm\:select-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.sm\:select-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.sm\:select-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.sm\:select-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.sm\:select-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem}.sm\:select-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem}.sm\:select-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem}.sm\:select-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem}.sm\:select-xl{--size:calc(var(--size-field,.25rem)*14);font-size:1.375rem}}@media (width>=768px){.md\:select{border:var(--border)solid #0000;appearance:none;background-color:var(--color-base-100);vertical-align:middle;width:clamp(3rem,20rem,100%);height:var(--size);touch-action:manipulation;text-overflow:ellipsis;box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;border-color:var(--input-color);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);--size:calc(var(--size-field,.25rem)*10);background-image:linear-gradient(45deg,#0000 50%,currentColor 50%),linear-gradient(135deg,currentColor 50%,#0000 50%);background-position:calc(100% - 20px) calc(1px + 50%),calc(100% - 16.1px) calc(1px + 50%);background-repeat:no-repeat;background-size:4px 4px,4px 4px;border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-shrink:1;align-items:center;gap:.375rem;padding-inline:1rem 1.75rem;font-size:.875rem;display:inline-flex;position:relative;[dir=rtl] &{background-position:12px calc(1px + 50%),16px calc(1px + 50%)}& select{appearance:none;background:inherit;border-radius:inherit;border-style:none;width:calc(100% + 2.75rem);height:calc(100% - 2px);margin-inline:-1rem -1.75rem;padding-inline:1rem 1.75rem;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:not(:last-child){background-image:none;margin-inline-end:-1.375rem}}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}&:has(>select[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>select[disabled])>select[disabled]{cursor:not-allowed}}.md\:select-ghost{box-shadow:none;background-color:#0000;border-color:#0000;transition:background-color .2s;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.md\:select-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.md\:select-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.md\:select-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.md\:select-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.md\:select-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.md\:select-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.md\:select-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.md\:select-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.md\:select-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem}.md\:select-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem}.md\:select-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem}.md\:select-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem}.md\:select-xl{--size:calc(var(--size-field,.25rem)*14);font-size:1.375rem}}@media (width>=1024px){.lg\:select{border:var(--border)solid #0000;appearance:none;background-color:var(--color-base-100);vertical-align:middle;width:clamp(3rem,20rem,100%);height:var(--size);touch-action:manipulation;text-overflow:ellipsis;box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;border-color:var(--input-color);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);--size:calc(var(--size-field,.25rem)*10);background-image:linear-gradient(45deg,#0000 50%,currentColor 50%),linear-gradient(135deg,currentColor 50%,#0000 50%);background-position:calc(100% - 20px) calc(1px + 50%),calc(100% - 16.1px) calc(1px + 50%);background-repeat:no-repeat;background-size:4px 4px,4px 4px;border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-shrink:1;align-items:center;gap:.375rem;padding-inline:1rem 1.75rem;font-size:.875rem;display:inline-flex;position:relative;[dir=rtl] &{background-position:12px calc(1px + 50%),16px calc(1px + 50%)}& select{appearance:none;background:inherit;border-radius:inherit;border-style:none;width:calc(100% + 2.75rem);height:calc(100% - 2px);margin-inline:-1rem -1.75rem;padding-inline:1rem 1.75rem;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:not(:last-child){background-image:none;margin-inline-end:-1.375rem}}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}&:has(>select[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>select[disabled])>select[disabled]{cursor:not-allowed}}.lg\:select-ghost{box-shadow:none;background-color:#0000;border-color:#0000;transition:background-color .2s;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.lg\:select-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.lg\:select-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.lg\:select-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.lg\:select-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.lg\:select-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.lg\:select-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.lg\:select-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.lg\:select-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.lg\:select-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem}.lg\:select-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem}.lg\:select-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem}.lg\:select-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem}.lg\:select-xl{--size:calc(var(--size-field,.25rem)*14);font-size:1.375rem}}@media (width>=1280px){.xl\:select{border:var(--border)solid #0000;appearance:none;background-color:var(--color-base-100);vertical-align:middle;width:clamp(3rem,20rem,100%);height:var(--size);touch-action:manipulation;text-overflow:ellipsis;box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;border-color:var(--input-color);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);--size:calc(var(--size-field,.25rem)*10);background-image:linear-gradient(45deg,#0000 50%,currentColor 50%),linear-gradient(135deg,currentColor 50%,#0000 50%);background-position:calc(100% - 20px) calc(1px + 50%),calc(100% - 16.1px) calc(1px + 50%);background-repeat:no-repeat;background-size:4px 4px,4px 4px;border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-shrink:1;align-items:center;gap:.375rem;padding-inline:1rem 1.75rem;font-size:.875rem;display:inline-flex;position:relative;[dir=rtl] &{background-position:12px calc(1px + 50%),16px calc(1px + 50%)}& select{appearance:none;background:inherit;border-radius:inherit;border-style:none;width:calc(100% + 2.75rem);height:calc(100% - 2px);margin-inline:-1rem -1.75rem;padding-inline:1rem 1.75rem;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:not(:last-child){background-image:none;margin-inline-end:-1.375rem}}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}&:has(>select[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>select[disabled])>select[disabled]{cursor:not-allowed}}.xl\:select-ghost{box-shadow:none;background-color:#0000;border-color:#0000;transition:background-color .2s;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.xl\:select-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.xl\:select-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.xl\:select-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.xl\:select-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.xl\:select-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.xl\:select-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.xl\:select-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.xl\:select-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.xl\:select-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem}.xl\:select-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem}.xl\:select-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem}.xl\:select-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem}.xl\:select-xl{--size:calc(var(--size-field,.25rem)*14);font-size:1.375rem}}@media (width>=1536px){.\32 xl\:select{border:var(--border)solid #0000;appearance:none;background-color:var(--color-base-100);vertical-align:middle;width:clamp(3rem,20rem,100%);height:var(--size);touch-action:manipulation;text-overflow:ellipsis;box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;border-color:var(--input-color);--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);--size:calc(var(--size-field,.25rem)*10);background-image:linear-gradient(45deg,#0000 50%,currentColor 50%),linear-gradient(135deg,currentColor 50%,#0000 50%);background-position:calc(100% - 20px) calc(1px + 50%),calc(100% - 16.1px) calc(1px + 50%);background-repeat:no-repeat;background-size:4px 4px,4px 4px;border-start-start-radius:var(--join-ss,var(--radius-field));border-start-end-radius:var(--join-se,var(--radius-field));border-end-end-radius:var(--join-ee,var(--radius-field));border-end-start-radius:var(--join-es,var(--radius-field));flex-shrink:1;align-items:center;gap:.375rem;padding-inline:1rem 1.75rem;font-size:.875rem;display:inline-flex;position:relative;[dir=rtl] &{background-position:12px calc(1px + 50%),16px calc(1px + 50%)}& select{appearance:none;background:inherit;border-radius:inherit;border-style:none;width:calc(100% + 2.75rem);height:calc(100% - 2px);margin-inline:-1rem -1.75rem;padding-inline:1rem 1.75rem;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:not(:last-child){background-image:none;margin-inline-end:-1.375rem}}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate;z-index:1}&:has(>select[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>select[disabled])>select[disabled]{cursor:not-allowed}}.\32 xl\:select-ghost{box-shadow:none;background-color:#0000;border-color:#0000;transition:background-color .2s;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.\32 xl\:select-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.\32 xl\:select-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.\32 xl\:select-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.\32 xl\:select-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.\32 xl\:select-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.\32 xl\:select-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.\32 xl\:select-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.\32 xl\:select-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.\32 xl\:select-xs{--size:calc(var(--size-field,.25rem)*6);font-size:.6875rem}.\32 xl\:select-sm{--size:calc(var(--size-field,.25rem)*8);font-size:.75rem}.\32 xl\:select-md{--size:calc(var(--size-field,.25rem)*10);font-size:.875rem}.\32 xl\:select-lg{--size:calc(var(--size-field,.25rem)*12);font-size:1.125rem}.\32 xl\:select-xl{--size:calc(var(--size-field,.25rem)*14);font-size:1.375rem}}}