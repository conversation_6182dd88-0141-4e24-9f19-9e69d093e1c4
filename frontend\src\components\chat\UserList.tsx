'use client';

import { useChat } from '@/contexts/ChatContext';
import { useAuth } from '@/contexts/AuthContext';

export default function UserList() {
  const { currentRoom, onlineUsers } = useChat();
  const { user } = useAuth();

  if (!currentRoom) {
    return (
      <div className="p-4 text-center text-base-content/50">
        <p>Select a room to see participants</p>
      </div>
    );
  }

  const roomParticipants = currentRoom.participants || [];
  const allUsers = [...roomParticipants];

  // Add online users who might not be in the room participants list
  onlineUsers.forEach(onlineUser => {
    if (!allUsers.find(u => u.id === onlineUser.id)) {
      allUsers.push(onlineUser);
    }
  });

  // Sort users: current user first, then online users, then offline users
  const sortedUsers = allUsers.sort((a, b) => {
    if (a.id === user?.id) return -1;
    if (b.id === user?.id) return 1;
    
    const aOnline = onlineUsers.some(u => u.id === a.id);
    const bOnline = onlineUsers.some(u => u.id === b.id);
    
    if (aOnline && !bOnline) return -1;
    if (!aOnline && bOnline) return 1;
    
    return a.display_name.localeCompare(b.display_name);
  });

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-base-300">
        <h3 className="font-semibold text-base-content/80">
          Participants ({sortedUsers.length})
        </h3>
      </div>

      {/* Users list */}
      <div className="flex-1 overflow-y-auto p-2">
        <div className="space-y-1">
          {sortedUsers.map((participant) => {
            const isOnline = onlineUsers.some(u => u.id === participant.id);
            const isCurrentUser = participant.id === user?.id;

            return (
              <div
                key={participant.id}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-base-300 transition-colors"
              >
                {/* Avatar with online indicator */}
                <div className="relative">
                  <div className="avatar">
                    <div className="w-8 h-8 rounded-full bg-primary text-primary-content flex items-center justify-center">
                      {participant.avatar_url ? (
                        <img 
                          src={participant.avatar_url} 
                          alt={participant.display_name}
                          className="w-full h-full rounded-full"
                        />
                      ) : (
                        <span className="text-xs font-medium">
                          {participant.display_name.charAt(0).toUpperCase()}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {/* Online indicator */}
                  <div className={`
                    absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-base-100
                    ${isOnline ? 'bg-success' : 'bg-base-300'}
                  `} />
                </div>

                {/* User info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium truncate">
                      {participant.display_name}
                      {isCurrentUser && (
                        <span className="text-xs text-base-content/50 ml-1">(You)</span>
                      )}
                    </p>
                    {participant.is_admin && (
                      <span className="badge badge-primary badge-xs">Admin</span>
                    )}
                  </div>
                  <p className="text-xs text-base-content/70 truncate">
                    @{participant.username}
                  </p>
                </div>

                {/* Status */}
                <div className="text-xs text-base-content/50">
                  {isOnline ? 'Online' : 'Offline'}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
