'use client';

import { useState, useEffect } from 'react';
import { Palette, Check } from 'lucide-react';
import { Theme } from '@/lib/types';
import { getTheme, setTheme } from '@/lib/utils';

const THEMES: { name: Theme; label: string; colors: string[] }[] = [
  { name: 'light', label: 'Light', colors: ['#ffffff', '#f3f4f6', '#1f2937'] },
  { name: 'dark', label: 'Dark', colors: ['#1f2937', '#374151', '#ffffff'] },
  { name: 'cupcake', label: 'Cupcake', colors: ['#faf7f5', '#e0e7ff', '#65a30d'] },
  { name: 'bumblebee', label: 'Bumblebee', colors: ['#fffbeb', '#fef3c7', '#d97706'] },
  { name: 'emerald', label: 'Emerald', colors: ['#ecfdf5', '#d1fae5', '#059669'] },
  { name: 'corporate', label: 'Corporate', colors: ['#f8fafc', '#e2e8f0', '#3b82f6'] },
  { name: 'synthwave', label: 'Synthwave', colors: ['#1a0b2e', '#7209b7', '#f72585'] },
  { name: 'retro', label: 'Retro', colors: ['#fef7ed', '#fed7aa', '#ea580c'] },
  { name: 'cyberpunk', label: 'Cyberpunk', colors: ['#0f0f0f', '#ffff00', '#ff0080'] },
  { name: 'valentine', label: 'Valentine', colors: ['#fdf2f8', '#fce7f3', '#ec4899'] },
  { name: 'halloween', label: 'Halloween', colors: ['#1a1a1a', '#f97316', '#8b5cf6'] },
  { name: 'garden', label: 'Garden', colors: ['#f0fdf4', '#dcfce7', '#16a34a'] },
  { name: 'forest', label: 'Forest', colors: ['#14532d', '#166534', '#22c55e'] },
  { name: 'aqua', label: 'Aqua', colors: ['#f0fdfa', '#ccfbf1', '#0891b2'] },
  { name: 'lofi', label: 'Lo-Fi', colors: ['#f5f5f4', '#e7e5e4', '#78716c'] },
  { name: 'pastel', label: 'Pastel', colors: ['#fefce8', '#fef3c7', '#a3a3a3'] },
  { name: 'fantasy', label: 'Fantasy', colors: ['#faf5ff', '#f3e8ff', '#a855f7'] },
  { name: 'wireframe', label: 'Wireframe', colors: ['#ffffff', '#e5e5e5', '#000000'] },
  { name: 'black', label: 'Black', colors: ['#000000', '#1f1f1f', '#ffffff'] },
  { name: 'luxury', label: 'Luxury', colors: ['#09090b', '#27272a', '#fbbf24'] },
  { name: 'dracula', label: 'Dracula', colors: ['#282a36', '#44475a', '#f8f8f2'] },
  { name: 'cmyk', label: 'CMYK', colors: ['#ffffff', '#e5e7eb', '#0891b2'] },
  { name: 'autumn', label: 'Autumn', colors: ['#fef7ed', '#fed7aa', '#ea580c'] },
  { name: 'business', label: 'Business', colors: ['#f8fafc', '#e2e8f0', '#1e293b'] },
  { name: 'acid', label: 'Acid', colors: ['#f0f0f0', '#ff00ff', '#00ff00'] },
  { name: 'lemonade', label: 'Lemonade', colors: ['#fffbeb', '#fef3c7', '#65a30d'] },
  { name: 'night', label: 'Night', colors: ['#0f172a', '#1e293b', '#f1f5f9'] },
  { name: 'coffee', label: 'Coffee', colors: ['#1c1917', '#44403c', '#fbbf24'] },
  { name: 'winter', label: 'Winter', colors: ['#f0f9ff', '#e0f2fe', '#0284c7'] },
];

interface ThemeSelectorProps {
  className?: string;
}

export default function ThemeSelector({ className = '' }: ThemeSelectorProps) {
  const [currentTheme, setCurrentTheme] = useState<Theme>('light');
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setCurrentTheme(getTheme() as Theme);
  }, []);

  const handleThemeChange = (theme: Theme) => {
    setTheme(theme);
    setCurrentTheme(theme);
    setIsOpen(false);
  };

  const currentThemeData = THEMES.find(t => t.name === currentTheme) || THEMES[0];

  return (
    <div className={`dropdown dropdown-end ${className}`}>
      <label tabIndex={0} className="btn btn-ghost btn-sm gap-2">
        <Palette className="h-4 w-4" />
        <span className="hidden sm:inline">{currentThemeData.label}</span>
        <div className="flex space-x-1">
          {currentThemeData.colors.map((color, index) => (
            <div
              key={index}
              className="w-3 h-3 rounded-full border border-base-content/20"
              style={{ backgroundColor: color }}
            />
          ))}
        </div>
      </label>
      
      <div tabIndex={0} className="dropdown-content z-[1] p-2 shadow-2xl bg-base-100 rounded-box w-80 max-h-96 overflow-y-auto">
        <div className="grid grid-cols-1 gap-2">
          <div className="text-sm font-medium text-base-content/70 px-2 py-1">
            Choose Theme
          </div>
          
          {THEMES.map((theme) => (
            <button
              key={theme.name}
              onClick={() => handleThemeChange(theme.name)}
              className={`
                flex items-center justify-between p-2 rounded-lg transition-colors
                hover:bg-base-200 ${currentTheme === theme.name ? 'bg-primary/10' : ''}
              `}
            >
              <div className="flex items-center space-x-3">
                <div className="flex space-x-1">
                  {theme.colors.map((color, index) => (
                    <div
                      key={index}
                      className="w-4 h-4 rounded-full border border-base-content/20"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
                <span className="text-sm font-medium">{theme.label}</span>
              </div>
              
              {currentTheme === theme.name && (
                <Check className="h-4 w-4 text-primary" />
              )}
            </button>
          ))}
        </div>
        
        <div className="divider my-2"></div>
        
        <div className="text-xs text-base-content/50 px-2">
          Theme changes apply immediately and are saved to your browser
        </div>
      </div>
    </div>
  );
}
