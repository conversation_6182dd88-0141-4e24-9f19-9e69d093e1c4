'use client';

import { useState } from 'react';
import { Menu, X, Search } from 'lucide-react';
import { useChat } from '@/contexts/ChatContext';
import Sidebar from './Sidebar';
import Chat<PERSON>rea from './ChatArea';
import UserList from './UserList';
import MessageSearch from './MessageSearch';

export default function ChatLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userListOpen, setUserListOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const { currentRoom } = useChat();

  return (
    <div className="h-screen flex bg-base-100">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed lg:static inset-y-0 left-0 z-50 w-80 bg-base-200 border-r border-base-300
        transform transition-transform duration-300 ease-in-out lg:translate-x-0
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex items-center justify-between p-4 border-b border-base-300 lg:hidden">
          <h2 className="text-lg font-semibold">Chat Rooms</h2>
          <button
            onClick={() => setSidebarOpen(false)}
            className="btn btn-ghost btn-sm"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <Sidebar />
      </div>

      {/* Main chat area */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Chat header */}
        <div className="bg-base-100 border-b border-base-300 px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setSidebarOpen(true)}
              className="btn btn-ghost btn-sm lg:hidden"
            >
              <Menu className="h-5 w-5" />
            </button>
            
            <div>
              <h1 className="text-lg font-semibold">
                {currentRoom ? currentRoom.name : 'Select a room'}
              </h1>
              {currentRoom?.description && (
                <p className="text-sm text-base-content/70">
                  {currentRoom.description}
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setUserListOpen(!userListOpen)}
              className="btn btn-ghost btn-sm"
            >
              Users
            </button>
          </div>
        </div>

        {/* Chat content */}
        <div className="flex-1 flex min-h-0">
          <div className="flex-1">
            <ChatArea />
          </div>
          
          {/* User list */}
          {userListOpen && (
            <div className="w-64 bg-base-200 border-l border-base-300">
              <UserList />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
