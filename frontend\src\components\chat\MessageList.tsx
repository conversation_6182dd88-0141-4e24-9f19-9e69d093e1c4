'use client';

import { useState } from 'react';
import { MoreVertical, Edit, Trash2, Reply } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useChat } from '@/contexts/ChatContext';
import { Message } from '@/lib/types';
import { formatTime, formatDate, isImageFile } from '@/lib/utils';
import MessageReactions from './MessageReactions';
import MessageAttachment from './MessageAttachment';
import ImageGallery from './ImageGallery';
import EmojiPicker from './EmojiPicker';

interface MessageListProps {
  messages: Message[];
}

export default function MessageList({ messages }: MessageListProps) {
  const { user } = useAuth();
  const { editMessage, deleteMessage, addReaction } = useChat();
  const [editingMessage, setEditingMessage] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState<string | null>(null);
  const [showImageGallery, setShowImageGallery] = useState(false);
  const [galleryImages, setGalleryImages] = useState<any[]>([]);
  const [galleryInitialIndex, setGalleryInitialIndex] = useState(0);

  const handleEditStart = (message: Message) => {
    setEditingMessage(message.id);
    setEditContent(message.content);
  };

  const handleEditSave = async (messageId: string) => {
    if (editContent.trim()) {
      await editMessage(messageId, editContent.trim());
      setEditingMessage(null);
      setEditContent('');
    }
  };

  const handleEditCancel = () => {
    setEditingMessage(null);
    setEditContent('');
  };

  const handleDelete = async (messageId: string) => {
    if (confirm('Are you sure you want to delete this message?')) {
      await deleteMessage(messageId);
    }
  };

  const handleEmojiSelect = async (messageId: string, emoji: string) => {
    await addReaction(messageId, emoji);
    setShowEmojiPicker(null);
  };

  const handleImageClick = (attachment: any, allImages: any[]) => {
    const initialIndex = allImages.findIndex(img => img.id === attachment.id);
    setGalleryImages(allImages);
    setGalleryInitialIndex(Math.max(0, initialIndex));
    setShowImageGallery(true);
  };

  const groupMessagesByDate = (messages: Message[]) => {
    const groups: { [date: string]: Message[] } = {};
    
    messages.forEach(message => {
      const date = new Date(message.created_at).toDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);
    });
    
    return groups;
  };

  const messageGroups = groupMessagesByDate(messages);

  if (messages.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-base-content/50">
        <div className="text-center">
          <div className="text-4xl mb-2">💭</div>
          <p>No messages yet. Start the conversation!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {Object.entries(messageGroups).map(([date, groupMessages]) => (
        <div key={date}>
          {/* Date separator */}
          <div className="flex items-center justify-center my-4">
            <div className="bg-base-200 px-3 py-1 rounded-full text-xs text-base-content/70">
              {formatDate(date)}
            </div>
          </div>

          {/* Messages for this date */}
          <div className="space-y-4">
            {groupMessages.map((message, index) => {
              const isOwn = message.sender_id === user?.id;
              const showAvatar = index === 0 || 
                groupMessages[index - 1].sender_id !== message.sender_id;

              return (
                <div
                  key={message.id}
                  className={`flex ${isOwn ? 'justify-end' : 'justify-start'} group`}
                >
                  <div className={`flex max-w-[70%] ${isOwn ? 'flex-row-reverse' : 'flex-row'}`}>
                    {/* Avatar */}
                    <div className={`flex-shrink-0 ${isOwn ? 'ml-2' : 'mr-2'}`}>
                      {showAvatar ? (
                        <div className="avatar">
                          <div className="w-8 h-8 rounded-full bg-primary text-primary-content flex items-center justify-center">
                            {message.sender.avatar_url ? (
                              <img 
                                src={message.sender.avatar_url} 
                                alt={message.sender.display_name}
                                className="w-full h-full rounded-full"
                              />
                            ) : (
                              <span className="text-xs font-medium">
                                {message.sender.display_name.charAt(0).toUpperCase()}
                              </span>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className="w-8 h-8" />
                      )}
                    </div>

                    {/* Message content */}
                    <div className={`flex flex-col ${isOwn ? 'items-end' : 'items-start'}`}>
                      {/* Sender name and time */}
                      {showAvatar && (
                        <div className={`flex items-center space-x-2 mb-1 ${isOwn ? 'flex-row-reverse space-x-reverse' : ''}`}>
                          <span className="text-sm font-medium text-base-content/80">
                            {message.sender.display_name}
                          </span>
                          <span className="text-xs text-base-content/50">
                            {formatTime(message.created_at)}
                          </span>
                        </div>
                      )}

                      {/* Message bubble */}
                      <div className="relative">
                        <div
                          className={`
                            px-4 py-2 rounded-2xl max-w-full break-words
                            ${isOwn 
                              ? 'bg-primary text-primary-content' 
                              : 'bg-base-200 text-base-content'
                            }
                            ${editingMessage === message.id ? 'ring-2 ring-primary' : ''}
                          `}
                        >
                          {editingMessage === message.id ? (
                            <div className="space-y-2">
                              <textarea
                                value={editContent}
                                onChange={(e) => setEditContent(e.target.value)}
                                className="textarea textarea-bordered w-full min-h-[60px] text-sm"
                                autoFocus
                              />
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => handleEditSave(message.id)}
                                  className="btn btn-primary btn-xs"
                                >
                                  Save
                                </button>
                                <button
                                  onClick={handleEditCancel}
                                  className="btn btn-ghost btn-xs"
                                >
                                  Cancel
                                </button>
                              </div>
                            </div>
                          ) : (
                            <>
                              <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                              {message.is_edited && (
                                <span className="text-xs opacity-70 ml-2">(edited)</span>
                              )}
                            </>
                          )}
                        </div>

                        {/* Message actions */}
                        {!editingMessage && (
                          <div className={`
                            absolute top-0 opacity-0 group-hover:opacity-100 transition-opacity
                            ${isOwn ? '-left-12' : '-right-12'}
                          `}>
                            <div className="flex space-x-1">
                              <button
                                onClick={() => setShowEmojiPicker(message.id)}
                                className="btn btn-ghost btn-xs"
                                title="Add reaction"
                              >
                                😊
                              </button>
                              
                              {isOwn && (
                                <div className="dropdown dropdown-end">
                                  <label tabIndex={0} className="btn btn-ghost btn-xs">
                                    <MoreVertical className="h-3 w-3" />
                                  </label>
                                  <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-32">
                                    <li>
                                      <button
                                        onClick={() => handleEditStart(message)}
                                        className="flex items-center space-x-2"
                                      >
                                        <Edit className="h-3 w-3" />
                                        <span>Edit</span>
                                      </button>
                                    </li>
                                    <li>
                                      <button
                                        onClick={() => handleDelete(message.id)}
                                        className="flex items-center space-x-2 text-error"
                                      >
                                        <Trash2 className="h-3 w-3" />
                                        <span>Delete</span>
                                      </button>
                                    </li>
                                  </ul>
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        {/* Emoji picker */}
                        {showEmojiPicker === message.id && (
                          <div className="absolute top-full mt-2 z-10">
                            <EmojiPicker
                              onEmojiSelect={(emoji) => handleEmojiSelect(message.id, emoji)}
                              onClose={() => setShowEmojiPicker(null)}
                            />
                          </div>
                        )}
                      </div>

                      {/* Reactions */}
                      {message.reactions.length > 0 && (
                        <div className="mt-1">
                          <MessageReactions
                            reactions={message.reactions}
                            messageId={message.id}
                          />
                        </div>
                      )}

                      {/* Attachments */}
                      {message.attachments.length > 0 && (
                        <div className="mt-2 space-y-2">
                          {message.attachments.map((attachment) => (
                            <MessageAttachment
                              key={attachment.id}
                              attachment={attachment}
                              allAttachments={message.attachments}
                              onImageClick={handleImageClick}
                            />
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ))}

      {/* Image Gallery */}
      <ImageGallery
        images={galleryImages}
        initialIndex={galleryInitialIndex}
        isOpen={showImageGallery}
        onClose={() => setShowImageGallery(false)}
      />
    </div>
  );
}
